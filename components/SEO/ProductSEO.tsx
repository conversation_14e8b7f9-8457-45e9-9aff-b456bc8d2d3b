import { Metadata } from 'next';
import { Locale } from '@/lib/i18n';
import { getPageKeywords } from '@/lib/main-keywords';

interface Product {
  id: number;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  category?: {
    name: string;
    name_ar: string;
  };
  subcategory?: {
    name: string;
    name_ar: string;
  };
}

interface ProductSEOProps {
  product: Product;
  locale: Locale;
}

export function generateProductMetadata(
  product: Product,
  locale: Locale
): Metadata {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  
  const productName = locale === 'ar' ? product.name_ar : product.name;
  const productDescription = locale === 'ar' ? product.description_ar : product.description;
  const categoryName = locale === 'ar' ? product.category?.name_ar : product.category?.name;
  
  // إنشاء العنوان
  const title = locale === 'ar'
    ? `${productName} - تجهيزات فندقية عالية الجودة | ${siteName}`
    : `${productName} - High Quality Hotel Equipment | ${siteName}`;

  // إنشاء الوصف
  const description = locale === 'ar'
    ? `${productName} من ${siteName} - ${productDescription || 'تجهيزات فندقية عالية الجودة'}. ${categoryName ? `ضمن فئة ${categoryName}` : ''}. احصل على عرض سعر مخصص لمشروعك الفندقي. نخدم فنادق مكة والمدينة والرياض وجدة.`
    : `${productName} from ${siteName} - ${productDescription || 'High quality hotel equipment'}. ${categoryName ? `In ${categoryName} category` : ''}. Get a custom quote for your hotel project. We serve hotels in Makkah, Madinah, Riyadh, and Jeddah.`;

  // إنشاء الكلمات المفتاحية
  const baseKeywords = getPageKeywords('products', locale);
  const productSpecificKeywords = locale === 'ar' 
    ? [
        productName,
        `${productName} فندقي`,
        `${productName} للفنادق`,
        categoryName ? `${categoryName} فندقية` : '',
        'تجهيزات فندقية',
        'مستلزمات الفنادق',
        'معدات الفنادق',
        'مورد تجهيزات فنادق',
        'عرض سعر فندقي'
      ].filter(Boolean)
    : [
        productName,
        `hotel ${productName.toLowerCase()}`,
        `${productName.toLowerCase()} for hotels`,
        categoryName ? `hotel ${categoryName.toLowerCase()}` : '',
        'hotel equipment',
        'hotel supplies',
        'hospitality supplies',
        'hotel supply supplier',
        'hotel equipment quotation'
      ].filter(Boolean);

  const keywords = [...baseKeywords, ...productSpecificKeywords].join(', ');

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      url: `${baseUrl}/${locale}/product/${product.id}`,
      siteName,
      type: 'website',
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    },
    alternates: {
      canonical: `${baseUrl}/${locale}/product/${product.id}`,
      languages: {
        'ar': `${baseUrl}/ar/product/${product.id}`,
        'en': `${baseUrl}/en/product/${product.id}`,
      },
    },
  };
}

// مكون لـ JSON-LD للمنتج
export function ProductJsonLd({ product, locale }: ProductSEOProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  
  const productName = locale === 'ar' ? product.name_ar : product.name;
  const productDescription = locale === 'ar' ? product.description_ar : product.description;
  
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: productName,
    description: productDescription || (locale === 'ar' 
      ? 'تجهيزات فندقية عالية الجودة من دروب هجر'
      : 'High quality hotel equipment from DROOB HAJER'
    ),
    brand: {
      '@type': 'Brand',
      name: siteName,
    },
    manufacturer: {
      '@type': 'Organization',
      name: siteName,
      url: baseUrl,
    },
    category: product.category ? (locale === 'ar' ? product.category.name_ar : product.category.name) : undefined,
    url: `${baseUrl}/${locale}/product/${product.id}`,
    offers: {
      '@type': 'Offer',
      availability: 'https://schema.org/InStock',
      priceCurrency: 'SAR',
      seller: {
        '@type': 'Organization',
        name: siteName,
        url: baseUrl,
      },
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
    />
  );
}

// دالة لإنشاء كلمات مفتاحية خاصة بالمنتج
export function generateProductKeywords(
  product: Product,
  locale: Locale
): string[] {
  const productName = locale === 'ar' ? product.name_ar : product.name;
  const categoryName = locale === 'ar' ? product.category?.name_ar : product.category?.name;
  const subcategoryName = locale === 'ar' ? product.subcategory?.name_ar : product.subcategory?.name;

  const baseKeywords = locale === 'ar'
    ? ['تجهيزات فندقية', 'مستلزمات الفنادق', 'معدات الفنادق', 'مورد تجهيزات فنادق']
    : ['hotel equipment', 'hotel supplies', 'hospitality supplies', 'hotel supply supplier'];

  const productKeywords = [
    productName,
    categoryName,
    subcategoryName,
    ...(locale === 'ar'
      ? [
          `${productName} فندقي`,
          `${productName} للفنادق`,
          categoryName ? `${categoryName} فندقية` : '',
          subcategoryName ? `${subcategoryName} فندقية` : ''
        ]
      : [
          `hotel ${productName.toLowerCase()}`,
          `${productName.toLowerCase()} for hotels`,
          categoryName ? `hotel ${categoryName.toLowerCase()}` : '',
          subcategoryName ? `hotel ${subcategoryName.toLowerCase()}` : ''
        ]
    )
  ].filter((keyword): keyword is string => Boolean(keyword));

  return [...baseKeywords, ...productKeywords];
}
