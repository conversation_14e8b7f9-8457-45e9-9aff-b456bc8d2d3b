'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Locale } from '../lib/i18n';
import { Category } from '../types/mysql-database';

interface CategoriesSectionProps {
  locale: Locale;
  categories?: Category[];
}

const CategoriesSection: React.FC<CategoriesSectionProps> = ({ locale, categories: initialCategories }) => {
  const [categories, setCategories] = useState<Category[]>(initialCategories || []);
  const [loading, setLoading] = useState(!initialCategories);
  const [visibleCategories, setVisibleCategories] = useState<Set<number>>(new Set());
  const [hoveredCategory, setHoveredCategory] = useState<number | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const categoryRefs = useRef<(HTMLElement | null)[]>([]);

  console.log('🔍 CategoriesSection - initialCategories:', initialCategories);
  console.log('🔍 CategoriesSection - categories state:', categories);
  console.log('🔍 CategoriesSection - loading state:', loading);

  // دالة لجلب الفئات من API
  const fetchCategories = async () => {
    try {
      setLoading(true);
      // إضافة timestamp لضمان عدم استخدام cache
      const timestamp = Date.now();
      const response = await fetch(`/api/categories?_t=${timestamp}`, {
        cache: 'no-store', // منع التخزين المؤقت
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
      console.log('📡 استجابة الخادم:', response.status, response.ok);

      if (response.ok) {
        const result = await response.json();
        console.log('📦 استجابة API الفئات:', result);

        if (result.success && result.data) {
          const activeCategories = result.data.filter((cat: Category) => cat.is_active);
          console.log('✅ الفئات النشطة من API:', activeCategories.length);
          setCategories(activeCategories);
        } else {
          console.error('❌ فشل في جلب الفئات:', result);
          setCategories([]);
        }
      } else {
        console.error('❌ خطأ في الاستجابة:', response.status);
        setCategories([]);
      }
    } catch (error) {
      console.error('❌ خطأ في جلب الفئات:', error);
      setCategories([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // إذا تم تمرير البيانات كـ props، استخدمها أولاً
    if (initialCategories) {
      console.log('✅ استخدام الفئات من props:', initialCategories.length);
      const activeCategories = initialCategories.filter((cat: Category) => cat.is_active);
      console.log('✅ الفئات النشطة:', activeCategories.length);
      setCategories(activeCategories);
      setLoading(false);
    } else {
      // إذا لم تكن هناك بيانات من props، اجلبها من API
      console.log('🔄 جلب الفئات من API...');
      fetchCategories();
    }
  }, [initialCategories]);

  // تحديث البيانات كل 30 ثانية
  useEffect(() => {
    const interval = setInterval(() => {
      console.log('🔄 تحديث تلقائي للفئات...');
      fetchCategories();
    }, 30000); // 30 ثانية

    return () => clearInterval(interval);
  }, []);

  // الاستماع لأحداث التحديث من النوافذ الأخرى
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'categories_updated') {
        console.log('🔄 تحديث الفئات بناءً على حدث التخزين...');
        fetchCategories();
      }
    };

    const handleCustomEvent = (e: Event) => {
      const customEvent = e as CustomEvent;
      if (customEvent.detail?.type === 'categories_updated') {
        console.log('🔄 تحديث الفئات بناءً على حدث مخصص...');
        fetchCategories();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('categories_updated', handleCustomEvent);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('categories_updated', handleCustomEvent);
    };
  }, []);

  // إعداد Intersection Observer للأنيميشن
  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleCategories(prev => new Set([...prev, index]));
          }
        });
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);



  // مراقبة العناصر عند تحميل الفئات
  useEffect(() => {
    const currentRefs = categoryRefs.current;

    if (!loading && categories.length > 0 && observerRef.current) {
      currentRefs.forEach((ref) => {
        if (ref) {
          observerRef.current?.observe(ref);
        }
      });
    }

    return () => {
      if (observerRef.current) {
        currentRefs.forEach((ref) => {
          if (ref) {
            observerRef.current?.unobserve(ref);
          }
        });
      }
    };
  }, [loading, categories]);

  if (loading) {
    return (
      <section className="py-20 bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/20 border-t-primary mx-auto mb-6"></div>
              <div className="absolute inset-0 rounded-full h-16 w-16 border-4 border-transparent border-r-primary/40 mx-auto animate-pulse"></div>
            </div>
            <p className="text-gray-700 text-lg font-medium animate-pulse">
              {locale === 'ar' ? 'جاري تحميل الفئات...' : 'Loading categories...'}
            </p>
            <div className="mt-4 flex justify-center space-x-2">
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
              <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="relative py-24 bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/30 overflow-hidden">
      {/* خلفية متحركة محسنة */}
      <div className="absolute inset-0">
        {/* طبقة الخلفية الأساسية */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/80 via-blue-50/40 to-purple-50/40"></div>

        {/* عناصر متحركة */}
        <div className="absolute top-20 left-10 w-40 h-40 bg-gradient-to-br from-primary/20 to-blue-500/20 rounded-full animate-blob opacity-60"></div>
        <div className="absolute top-40 right-20 w-32 h-32 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full animate-blob animation-delay-2000 opacity-60"></div>
        <div className="absolute bottom-20 left-1/4 w-28 h-28 bg-gradient-to-br from-green-500/20 to-teal-500/20 rounded-full animate-blob animation-delay-4000 opacity-60"></div>
        <div className="absolute bottom-40 right-1/3 w-24 h-24 bg-gradient-to-br from-orange-500/20 to-red-500/20 rounded-full animate-blob animation-delay-1000 opacity-60"></div>

        {/* خطوط ديكورية */}
        <div className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-primary/10 to-transparent"></div>
        <div className="absolute top-0 right-1/4 w-px h-full bg-gradient-to-b from-transparent via-purple-500/10 to-transparent"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Header محسن */}
        <div className="text-center mb-20">
          {/* شارة القسم */}
          <div className="inline-flex items-center gap-3 bg-white border-2 border-primary/20 rounded-full px-6 py-3 mb-8 shadow-xl">
            <div className="w-8 h-8 bg-gradient-to-br from-primary to-purple-600 rounded-full flex items-center justify-center">
              <i className="ri-apps-2-line text-white text-sm"></i>
            </div>
            <span className="text-primary text-sm font-bold uppercase tracking-wider">
              {locale === 'ar' ? 'تصفح مجموعاتنا المتميزة' : 'Browse Our Premium Collections'}
            </span>
          </div>

          {/* العنوان الرئيسي */}
          <h2 className="text-5xl md:text-6xl lg:text-7xl font-black mb-8 leading-tight">
            <span className="text-gray-900">
              {locale === 'ar' ? 'فئات ' : 'Product '}
            </span>
            <span className="text-primary">
              {locale === 'ar' ? 'المنتجات' : 'Categories'}
            </span>
          </h2>

          {/* خط زخرفي متحرك */}
          <div className="flex items-center justify-center mb-8">
            <div className="h-1 w-16 bg-gradient-to-r from-transparent to-primary rounded-full"></div>
            <div className="w-4 h-4 bg-gradient-to-br from-primary to-purple-600 rounded-full mx-4 animate-pulse"></div>
            <div className="h-1 w-16 bg-gradient-to-l from-transparent to-purple-600 rounded-full"></div>
          </div>

          {/* الوصف */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-6 shadow-lg max-w-4xl mx-auto">
            <p className="text-gray-900 text-xl md:text-2xl leading-relaxed font-medium">
              {locale === 'ar'
                ? 'اكتشف مجموعتنا الاستثنائية من المعدات المتخصصة، مصنفة بعناية لتلبية جميع احتياجات المطاعم والفنادق العصرية'
                : 'Discover our exceptional collection of specialized equipment, carefully categorized to meet all modern restaurant and hotel needs'
              }
            </p>

            {/* زر التحديث */}
            <div className="mt-4 text-center">
              <button
                onClick={fetchCategories}
                disabled={loading}
                className="inline-flex items-center gap-2 bg-primary/10 hover:bg-primary hover:text-white text-primary px-4 py-2 rounded-lg transition-all duration-300 disabled:opacity-50"
              >
                <i className={`ri-refresh-line ${loading ? 'animate-spin' : ''}`}></i>
                <span className="text-sm font-medium">
                  {loading
                    ? (locale === 'ar' ? 'جاري التحديث...' : 'Updating...')
                    : (locale === 'ar' ? 'تحديث الفئات' : 'Refresh Categories')
                  }
                </span>
              </button>
            </div>
          </div>

          {/* إحصائيات سريعة */}
        
        </div>

        {/* شبكة الفئات المحسنة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {/* رسالة تشخيصية */}
          {categories.length === 0 && !loading && (
            <div className="col-span-full text-center py-12">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <i className="ri-information-line text-yellow-600 text-3xl mb-4"></i>
                <h3 className="text-lg font-semibold text-yellow-800 mb-2">
                  {locale === 'ar' ? 'لا توجد فئات متاحة' : 'No categories available'}
                </h3>
                <p className="text-yellow-700">
                  {locale === 'ar'
                    ? 'لم يتم العثور على أي فئات نشطة في قاعدة البيانات'
                    : 'No active categories found in the database'
                  }
                </p>
              </div>
            </div>
          )}

          {categories.map((category, index) => (
            <div
              key={category.id}
              ref={(el) => {categoryRefs.current[index] = el}}
              data-index={index}
              className={`group relative bg-white rounded-3xl shadow-xl overflow-hidden transform transition-all duration-700 hover:shadow-2xl hover:-translate-y-2 ${
                visibleCategories.has(index)
                  ? 'animate-fadeInUp opacity-100'
                  : 'opacity-0 translate-y-8'
              }`}
              style={{
                animationDelay: `${index * 0.15}s`
              }}
              onMouseEnter={() => setHoveredCategory(index)}
              onMouseLeave={() => setHoveredCategory(null)}
            >
              {/* تأثير الإضاءة الخارجية */}
              <div className={`absolute -inset-1 bg-gradient-to-r from-primary via-purple-600 to-primary rounded-3xl opacity-0 group-hover:opacity-20 transition-opacity duration-500 blur-sm ${hoveredCategory === index ? 'animate-pulse' : ''}`}></div>

              <Link
                href={`/${locale}/category/${category.id}`}
                className="relative block h-full"
              >
                {/* قسم الصورة المحسن */}
                <div className="relative h-64 overflow-hidden rounded-t-3xl">
                  {category.image_url && category.image_url.trim() ? (
                    category.image_url.startsWith('/uploads/') || category.image_url.startsWith('/api/') || category.image_url.startsWith('http://localhost') ? (
                      <Image
                        src={category.image_url}
                        alt={locale === 'ar' ? category.name_ar : category.name}
                        width={400}
                        height={300}
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/api/placeholder?width=400&height=300&text=خطأ في الصورة';
                        }}
                      />
                    ) : (
                      <Image
                        src={category.image_url}
                        alt={locale === 'ar' ? category.name_ar : category.name}
                        width={400}
                        height={300}
                        className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/api/placeholder?width=400&height=300&text=خطأ في الصورة';
                        }}
                      />
                    )
                  ) : (
                    <Image
                      src="/api/placeholder?width=400&height=300&text=صورة الفئة"
                      alt={locale === 'ar' ? category.name_ar : category.name}
                      width={400}
                      height={300}
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                    />
                  )}

                  {/* طبقة التراكب المتدرجة */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>

                  {/* تأثير الضوء المتحرك */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>

                  {/* شارة الفئة المحسنة */}
                  <div className="absolute top-6 right-6 z-20">
                    <div className="relative">
                      <div className="w-14 h-14 bg-white/95 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-xl group-hover:shadow-2xl transition-all duration-300 group-hover:scale-110">
                        <i className="ri-store-2-line text-primary text-xl group-hover:animate-bounce"></i>
                      </div>
                      <div className="absolute -inset-1 bg-gradient-to-r from-primary to-purple-600 rounded-2xl opacity-0 group-hover:opacity-30 transition-opacity duration-300 blur-sm"></div>
                    </div>
                  </div>

                  {/* رقم الفئة */}
                  <div className="absolute top-6 left-6 z-20">
                    <div className="w-10 h-10 bg-gradient-to-br from-primary to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-lg shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                      {index + 1}
                    </div>
                  </div>

                  {/* مؤشر المنتجات المتاحة */}
                  <div className="absolute bottom-6 right-6 z-20">
                    <div className="bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 border border-white/30 shadow-lg group-hover:shadow-xl transition-all duration-300">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span className="text-gray-800 text-sm font-semibold">
                          {locale === 'ar' ? 'متاح الآن' : 'Available'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* تأثير التمرير المحسن */}
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/30 via-purple-600/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500"></div>
                </div>

                {/* قسم المحتوى المحسن */}
                <div className="relative p-8 bg-gradient-to-br from-white to-gray-50/50">
                  {/* خلفية ديكورية */}
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary via-purple-600 to-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"></div>

                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      {/* العنوان */}
                      <h3 className="text-gray-900 font-bold text-2xl mb-3 group-hover:text-primary transition-colors duration-300 leading-tight">
                        {locale === 'ar' ? category.name_ar : category.name}
                      </h3>

                      {/* الوصف */}
                      <p className="text-gray-600 text-base leading-relaxed mb-4 group-hover:text-gray-700 transition-colors duration-300">
                        {
                         (locale === 'ar' ? 'اكتشف مجموعة متنوعة من المنتجات عالية الجودة' : 'Discover a variety of high-quality products')}
                      </p>

                      {/* ميزات سريعة */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {[
                          locale === 'ar' ? 'جودة عالية' : 'High Quality',
                          locale === 'ar' ? 'ضمان شامل' : 'Full Warranty',
                          locale === 'ar' ? 'توصيل سريع' : 'Fast Delivery'
                        ].map((feature, idx) => (
                          <span
                            key={idx}
                            className="px-3 py-1 bg-primary/10 text-primary text-xs font-medium rounded-full group-hover:bg-primary group-hover:text-white transition-all duration-300"
                            style={{ transitionDelay: `${idx * 100}ms` }}
                          >
                            {feature}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* زر الانتقال */}
                    <div className="ml-6">
                      <div className="w-14 h-14 bg-gradient-to-br from-primary/10 to-purple-600/10 rounded-2xl flex items-center justify-center group-hover:from-primary group-hover:to-purple-600 transition-all duration-300 group-hover:scale-110 group-hover:shadow-lg">
                        <i className="ri-arrow-right-line text-primary group-hover:text-white text-xl transition-all duration-300 group-hover:translate-x-1"></i>
                      </div>
                    </div>
                  </div>

                  {/* شريط التقدم التفاعلي */}
                  <div className="mt-6 relative">
                    <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
                      <div className="h-full bg-gradient-to-r from-primary via-purple-600 to-primary rounded-full transform -translate-x-full group-hover:translate-x-0 transition-transform duration-700 ease-out"></div>
                    </div>

                    {/* نقاط ديكورية */}
                    <div className="absolute -top-1 left-0 w-4 h-4 bg-primary rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:scale-110"></div>
                    <div className="absolute -top-1 right-0 w-4 h-4 bg-purple-600 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 transform group-hover:scale-110"></div>
                  </div>

                  {/* تأثير الإضاءة السفلية */}
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-3/4 h-px bg-gradient-to-r from-transparent via-primary/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </div>
              </Link>
            </div>
          ))}
        </div>

        {/* قسم الدعوة للعمل المحسن */}
        <div className="mt-24">
          <div className="relative bg-white rounded-3xl p-12 shadow-2xl border border-gray-200 max-w-5xl mx-auto">
            {/* خلفية ديكورية خفيفة */}
            <div className="absolute inset-0">
              <div className="absolute top-10 right-10 w-32 h-32 bg-primary/5 rounded-full animate-pulse"></div>
              <div className="absolute bottom-10 left-10 w-24 h-24 bg-purple-500/5 rounded-full animate-pulse animation-delay-2000"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-gray-100/50 rounded-full animate-spin-slow"></div>
            </div>

            <div className="relative z-10 text-center">
              {/* أيقونة مركزية */}
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-primary to-purple-600 rounded-full mb-8 shadow-xl">
                <i className="ri-search-2-line text-3xl text-white animate-bounce"></i>
              </div>

              {/* العنوان */}
              <h3 className="text-4xl md:text-5xl font-black mb-6 leading-tight text-gray-900">
                {locale === 'ar' ? 'لم تجد ما تبحث عنه؟' : "Can't find what you're looking for?"}
              </h3>

              {/* الوصف */}
              <p className="text-gray-700 text-xl mb-10 max-w-3xl mx-auto leading-relaxed">
                {locale === 'ar'
                  ? 'فريقنا من الخبراء جاهز لمساعدتك في العثور على الحلول المثالية. احصل على استشارة مجانية وحلول مخصصة تماماً لاحتياجاتك'
                  : 'Our team of experts is ready to help you find the perfect solutions. Get free consultation and solutions perfectly tailored to your needs'
                }
              </p>

              {/* الأزرار */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <button className="group bg-gradient-to-r from-primary to-purple-600 hover:from-purple-600 hover:to-primary text-white px-10 py-4 rounded-2xl font-bold text-lg transition-all duration-300 transform hover:scale-105 hover:shadow-2xl flex items-center gap-3">
                  <i className="ri-phone-line text-xl group-hover:animate-pulse"></i>
                  <span>{locale === 'ar' ? 'اتصل بنا الآن' : 'Call Us Now'}</span>
                  <i className="ri-arrow-right-line transition-transform duration-300 group-hover:translate-x-1"></i>
                </button>

                <button className="group border-2 border-primary text-primary hover:bg-primary hover:text-white px-10 py-4 rounded-2xl font-bold text-lg transition-all duration-300 transform hover:scale-105 flex items-center gap-3">
                  <i className="ri-chat-3-line text-xl group-hover:animate-bounce"></i>
                  <span>{locale === 'ar' ? 'دردشة مباشرة' : 'Live Chat'}</span>
                  <i className="ri-external-link-line transition-transform duration-300 group-hover:translate-x-1"></i>
                </button>
              </div>

              {/* معلومات إضافية */}
          
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CategoriesSection;
