'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Locale } from '../lib/i18n';
import { getTranslation } from '../lib/translations';

interface Subcategory {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  product_count: number;
}

interface Category {
  id: string;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  image_url?: string;
  is_active: boolean;
  subcategories: Subcategory[];
}

interface CategoriesDropdownProps {
  locale: Locale;
}

const CategoriesDropdown: React.FC<CategoriesDropdownProps> = ({ locale }) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // دالة الترجمة
  const t = (key: string) => getTranslation(locale, key as keyof typeof import('../lib/translations').translations.ar);

  // جلب الفئات من API
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        console.log('🔍 جلب الفئات من API...');
        const response = await fetch('/api/navbar/categories');
        console.log('📡 استجابة API:', response.status);

        const result = await response.json();
        console.log('📊 بيانات الفئات:', result);

        if (result.success) {
          setCategories(result.data);
          console.log(`✅ تم تحميل ${result.data.length} فئة`);
        } else {
          console.error('❌ فشل في جلب الفئات:', result.message);
        }
      } catch (error) {
        console.error('❌ خطأ في جلب الفئات:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setHoveredCategory(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  if (loading) {
    return (
      <div className="relative">
        <button className="flex items-center gap-2 font-semibold text-gray-700 hover:text-primary transition-colors duration-300">
          <i className="ri-menu-line"></i>
          <span>{t('categories')}</span>
          <i className="ri-arrow-down-s-line text-sm"></i>
        </button>
      </div>
    );
  }

  if (categories.length === 0) {
    // عرض الزر حتى لو لم توجد فئات (للتطوير)
    return (
      <div className="relative">
        <button className="flex items-center gap-2 font-semibold text-gray-700 hover:text-primary transition-colors duration-300">
          <i className="ri-menu-line"></i>
          <span>{t('categories')}</span>
          <i className="ri-arrow-down-s-line text-sm"></i>
          <span className="text-xs text-red-500 ml-1">(فارغ)</span>
        </button>
      </div>
    );
  }

  return (
    <div className="relative" ref={dropdownRef}>
      {/* زر الفئات المحسن */}
      <button
        className={`relative px-4 py-2 rounded-lg font-semibold transition-all duration-300 group ${
          isOpen
            ? 'text-white bg-gradient-to-r from-primary to-secondary shadow-lg'
            : 'text-gray-700 hover:text-primary hover:bg-primary/5'
        }`}
        onClick={() => setIsOpen(!isOpen)}
        onMouseEnter={() => setIsOpen(true)}
      >
        <span className="flex items-center gap-2 relative z-10">
          <i className={`ri-menu-line transition-transform duration-300 ${isOpen ? 'rotate-90' : ''}`}></i>
          <span>{t('categories')}</span>
          <i className={`ri-arrow-down-s-line text-sm transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}></i>
        </span>
        {!isOpen && (
          <span className="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg scale-0 group-hover:scale-100 transition-transform duration-300"></span>
        )}
      </button>

      {/* القائمة المنسدلة المحسنة */}
      {isOpen && (
        <div
          className={`absolute top-full mt-3 bg-white/95 backdrop-blur-lg rounded-2xl shadow-2xl border border-gray-200/50 overflow-hidden z-50 w-[850px] max-w-[95vw] transform transition-all duration-300 ${
            locale === 'ar' ? 'right-0 xl:right-[-100px]' : 'left-0'
          }`}
          style={{
            animation: 'slideDown 0.3s ease-out'
          }}
          onMouseLeave={() => {
            setIsOpen(false);
            setHoveredCategory(null);
          }}
        >
          <div className="flex">
            {/* قائمة الفئات الرئيسية */}
            <div className="w-1/3 bg-gray-50 border-r border-gray-200">
              <div className="p-4 border-b border-gray-200">
                <h3 className="font-bold text-gray-800">
                  {locale === 'ar' ? 'الفئات الرئيسية' : 'Main Categories'}
                </h3>
              </div>
              <div className="max-h-96 overflow-y-auto">
                {categories.map((category) => (
                  <div
                    key={category.id}
                    className={`p-3 cursor-pointer transition-colors duration-200 ${
                      hoveredCategory === category.id 
                        ? 'bg-primary/10 text-primary' 
                        : 'hover:bg-gray-100'
                    }`}
                    onMouseEnter={() => setHoveredCategory(category.id)}
                  >
                    <div className="flex items-center gap-3">
                      {category.image_url && (
                        <Image
                          src={category.image_url}
                          alt={locale === 'ar' ? category.name_ar : category.name}
                          width={32}
                          height={32}
                          className="w-8 h-8 rounded-lg object-cover"
                        />
                      )}
                      <div>
                        <div className="font-medium">
                          {locale === 'ar' ? category.name_ar : category.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {category.subcategories?.length || 0} {locale === 'ar' ? 'فئة فرعية' : 'subcategories'}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* قائمة الفئات الفرعية */}
            <div className="w-2/3">
              {hoveredCategory && (
                <div className="p-4">
                  {(() => {
                    const selectedCategory = categories.find(cat => cat.id === hoveredCategory);
                    if (!selectedCategory) return null;

                    return (
                      <>
                        <div className="mb-4 pb-3 border-b border-gray-200">
                          <h4 className="font-bold text-lg text-gray-800">
                            {locale === 'ar' ? selectedCategory.name_ar : selectedCategory.name}
                          </h4>
                          {selectedCategory.description && (
                            <p className="text-sm text-gray-600 mt-1">
                              {locale === 'ar' ? selectedCategory.description_ar : selectedCategory.description}
                            </p>
                          )}
                        </div>

                        {selectedCategory.subcategories && selectedCategory.subcategories.length > 0 ? (
                          <div className="grid grid-cols-2 gap-3 max-h-80 overflow-y-auto">
                            {selectedCategory.subcategories.map((subcategory) => (
                              <Link
                                key={subcategory.id}
                                href={`/${locale}/products?subcategory=${subcategory.id}`}
                                className="block p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 group"
                                onClick={() => {
                                  setIsOpen(false);
                                  setHoveredCategory(null);
                                }}
                              >
                                <div className="flex items-center gap-3">
                                  {subcategory.image_url && (
                                    <Image
                                      src={subcategory.image_url}
                                      alt={locale === 'ar' ? subcategory.name_ar : subcategory.name}
                                      width={40}
                                      height={40}
                                      className="w-10 h-10 rounded-lg object-cover"
                                    />
                                  )}
                                  <div className="flex-1">
                                    <div className="font-medium text-gray-800 group-hover:text-primary transition-colors">
                                      {locale === 'ar' ? subcategory.name_ar : subcategory.name}
                                    </div>
                                    <div className="text-xs text-gray-500">
                                      {subcategory.product_count} {locale === 'ar' ? 'منتج' : 'products'}
                                    </div>
                                  </div>
                                  <i className="ri-arrow-right-line text-gray-400 group-hover:text-primary transition-colors"></i>
                                </div>
                              </Link>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-8 text-gray-500">
                            <i className="ri-folder-open-line text-3xl mb-2"></i>
                            <p>{locale === 'ar' ? 'لا توجد فئات فرعية حالياً' : 'No subcategories available'}</p>
                          </div>
                        )}

                        {/* رابط عرض جميع منتجات الفئة */}
                        <div className="mt-4 pt-3 border-t border-gray-200">
                          <Link
                            href={`/${locale}/products?category=${selectedCategory.id}`}
                            className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors"
                            onClick={() => {
                              setIsOpen(false);
                              setHoveredCategory(null);
                            }}
                          >
                            <span>
                              {locale === 'ar' 
                                ? `عرض جميع منتجات ${selectedCategory.name_ar}` 
                                : `View all ${selectedCategory.name} products`
                              }
                            </span>
                            <i className="ri-arrow-right-line"></i>
                          </Link>
                        </div>
                      </>
                    );
                  })()}
                </div>
              )}

              {!hoveredCategory && (
                <div className="p-8 text-center text-gray-500">
                  <i className="ri-mouse-line text-3xl mb-2"></i>
                  <p>{locale === 'ar' ? 'مرر الماوس على فئة لعرض الفئات الفرعية' : 'Hover over a category to see subcategories'}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CategoriesDropdown;
