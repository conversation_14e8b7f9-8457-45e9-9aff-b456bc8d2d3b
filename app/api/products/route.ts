import { NextRequest, NextResponse } from 'next/server';
import {
  getProductsWithDetails,
  getFeaturedProductsWithDetails,
  getProductsByCategoryWithDetails,
  getProductsBySubcategoryWithDetails,
  getProductWithDetails
} from '@/lib/mysql-database';
import { ProductWithDetails } from '@/types/mysql-database';

// Cache بسيط في الذاكرة للتطوير
const cache = new Map<string, { data: unknown; timestamp: number; ttl: number }>();

function getCachedData<T>(key: string, fetcher: () => Promise<T>, ttl: number = 1800000): Promise<T> {
  const cached = cache.get(key);
  const now = Date.now();

  if (cached && (now - cached.timestamp) < cached.ttl) {
    return Promise.resolve(cached.data as T);
  }

  return fetcher().then(data => {
    cache.set(key, { data, timestamp: now, ttl });
    return data;
  });
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const featured = searchParams.get('featured');
    const categoryId = searchParams.get('categoryId');
    const subcategoryId = searchParams.get('subcategoryId');
    const id = searchParams.get('id');

    let products: ProductWithDetails[];

    // إذا تم تمرير ID محدد، جلب منتج واحد مع التفاصيل
    if (id && typeof id === 'string') {
      const product = await getProductWithDetails(id);
      if (!product) {
        return NextResponse.json({
          success: false,
          message: 'Product not found',
          messageAr: 'المنتج غير موجود'
        }, { status: 404 });
      }
      return NextResponse.json({ success: true, data: product });
    }

    // جلب المنتجات حسب الفلترة مع استخدام Cache
    if (featured === 'true') {
      products = await getCachedData('featured-products', () => getFeaturedProductsWithDetails(), 900000); // 15 دقيقة
    } else if (categoryId && typeof categoryId === 'string') {
      products = await getCachedData(`products-category-${categoryId}`, () => getProductsByCategoryWithDetails(categoryId));
    } else if (subcategoryId && typeof subcategoryId === 'string') {
      products = await getCachedData(`products-subcategory-${subcategoryId}`, () => getProductsBySubcategoryWithDetails(subcategoryId));
    } else {
      products = await getCachedData('all-products', () => getProductsWithDetails());
    }

    return NextResponse.json({
      success: true,
      data: products
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=1800, stale-while-revalidate=3600',
        'CDN-Cache-Control': 'public, s-maxage=1800',
        'Vercel-CDN-Cache-Control': 'public, s-maxage=1800'
      }
    });
  } catch (error) {
    console.error('Products API Error:', error);
    return NextResponse.json({ 
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
