import { NextRequest, NextResponse } from 'next/server';
import { getCategories, addCategory, updateCategory, deleteCategory, getCategoryById } from '../../../../lib/mysql-database';
import { v4 as uuidv4 } from 'uuid';

// GET - جلب جميع الفئات
export async function GET() {
  try {
    console.log('📥 API الإدارة - جلب الفئات');
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const categories = await getCategories();
    console.log('📦 API الإدارة - تم جلب الفئات:', categories.length);
    return NextResponse.json({ success: true, data: categories });

  } catch (error) {
    console.error('❌ API الإدارة - خطأ في جلب الفئات:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

// POST - إضافة فئة جديدة
export async function POST(request: NextRequest) {
  try {
    console.log('📥 API الإدارة - إضافة فئة جديدة');
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const body = await request.json();
    console.log('📦 API الإدارة - بيانات الفئة المستلمة:', body);
    const { name, nameAr, description, descriptionAr, image, isActive } = body;

    if (!name || !nameAr) {
      console.log('❌ API الإدارة - بيانات مفقودة:', { name, nameAr });
      return NextResponse.json({
        success: false,
        message: 'Name and Arabic name are required',
        messageAr: 'الاسم والاسم بالعربية مطلوبان'
      }, { status: 400 });
    }

    const categoryData = {
      id: uuidv4(),
      name,
      name_ar: nameAr,
      description: description || null,
      description_ar: descriptionAr || null,
      image_url: image || null,
      is_active: isActive !== undefined ? isActive : true
    };

    console.log('📤 API الإدارة - بيانات الفئة للحفظ:', categoryData);
    const newCategory = await addCategory(categoryData);
    console.log('✅ API الإدارة - تم إنشاء الفئة بنجاح:', newCategory);
    return NextResponse.json({ success: true, data: newCategory }, { status: 201 });

  } catch (error) {
    console.error('❌ API الإدارة - خطأ في إضافة الفئة:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

// PUT - تحديث فئة موجودة
export async function PUT(request: NextRequest) {
  try {
    console.log('📥 API الإدارة - تحديث فئة');
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const { searchParams } = new URL(request.url);
    const updateId = searchParams.get('id');
    console.log('🔍 API الإدارة - معرف الفئة للتحديث:', updateId);

    if (!updateId) {
      console.log('❌ API الإدارة - معرف الفئة مفقود');
      return NextResponse.json({
        success: false,
        message: 'Category ID is required',
        messageAr: 'معرف الفئة مطلوب'
      }, { status: 400 });
    }

    const existingCategory = await getCategoryById(updateId);
    console.log('🔍 API الإدارة - الفئة الموجودة:', existingCategory);
    if (!existingCategory) {
      console.log('❌ API الإدارة - الفئة غير موجودة');
      return NextResponse.json({
        success: false,
        message: 'Category not found',
        messageAr: 'الفئة غير موجودة'
      }, { status: 404 });
    }

    const body = await request.json();
    console.log('📦 API الإدارة - بيانات التحديث المستلمة:', body);
    const { name, nameAr, description, descriptionAr, image, isActive } = body;

    // تحويل أسماء الحقول لتتطابق مع قاعدة البيانات
    const updateData = {
      name,
      name_ar: nameAr,
      description,
      description_ar: descriptionAr,
      image_url: image,
      is_active: isActive
    };
    console.log('📤 API الإدارة - بيانات التحديث للحفظ:', updateData);

    const updatedCategory = await updateCategory(updateId, updateData);
    console.log('✅ API الإدارة - تم تحديث الفئة بنجاح:', updatedCategory);
    return NextResponse.json({
      success: true,
      data: updatedCategory,
      message: 'Category updated successfully',
      messageAr: 'تم تحديث الفئة بنجاح'
    });

  } catch (error) {
    console.error('❌ API الإدارة - خطأ في تحديث الفئة:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

// DELETE - حذف فئة
export async function DELETE(request: NextRequest) {
  try {
    console.log('📥 API الإدارة - حذف فئة');
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const { searchParams } = new URL(request.url);
    const deleteId = searchParams.get('id');
    console.log('🔍 API الإدارة - معرف الفئة للحذف:', deleteId);

    if (!deleteId) {
      console.log('❌ API الإدارة - معرف الفئة مفقود');
      return NextResponse.json({
        success: false,
        message: 'Category ID is required',
        messageAr: 'معرف الفئة مطلوب'
      }, { status: 400 });
    }

    console.log('🗑️ API الإدارة - محاولة حذف الفئة...');
    const deleted = await deleteCategory(deleteId);
    console.log('📦 API الإدارة - نتيجة الحذف:', deleted);

    if (!deleted) {
      console.log('❌ API الإدارة - الفئة غير موجودة');
      return NextResponse.json({
        success: false,
        message: 'Category not found',
        messageAr: 'الفئة غير موجودة'
      }, { status: 404 });
    }

    console.log('✅ API الإدارة - تم حذف الفئة بنجاح');
    return NextResponse.json({
      success: true,
      message: 'Category deleted successfully',
      messageAr: 'تم حذف الفئة بنجاح'
    });

  } catch (error) {
    console.error('❌ API الإدارة - خطأ في حذف الفئة:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
