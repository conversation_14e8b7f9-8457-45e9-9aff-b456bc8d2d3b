import { NextRequest, NextResponse } from 'next/server';
import {
  getSubcategories,
  addSubcategory,
  updateSubcategory,
  deleteSubcategory,
  getSubcategoryById,
  getSubcategoriesByCategory
} from '@/lib/mysql-database';
import { v4 as uuidv4 } from 'uuid';

// GET - جلب الفئات الفرعية
export async function GET(request: NextRequest) {
  try {
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId');

    let subcategories;
    if (categoryId && typeof categoryId === 'string') {
      subcategories = await getSubcategoriesByCategory(categoryId);
    } else {
      subcategories = await getSubcategories();
    }

    return NextResponse.json({ success: true, data: subcategories });
  } catch (error) {
    console.error('Admin Subcategories GET API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

// POST - إضافة فئة فرعية جديدة
export async function POST(request: NextRequest) {
  try {
    console.log('📥 API الإدارة - إضافة فئة فرعية جديدة');
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const body = await request.json();
    console.log('📦 API الإدارة - بيانات الفئة الفرعية المستلمة:', body);
    const { name, nameAr, categoryId: catId, description, descriptionAr, image, isActive } = body;

    if (!name || !nameAr || !catId) {
      console.log('❌ API الإدارة - بيانات مفقودة:', { name, nameAr, catId });
      return NextResponse.json({
        success: false,
        message: 'Name, Arabic name, and category ID are required',
        messageAr: 'الاسم والاسم بالعربية ومعرف الفئة مطلوبة'
      }, { status: 400 });
    }

    const subcategoryData = {
      id: uuidv4(),
      name,
      name_ar: nameAr,
      category_id: catId,
      description: description || null,
      description_ar: descriptionAr || null,
      image_url: image || null,
      is_active: isActive !== undefined ? isActive : true
    };

    console.log('📤 API الإدارة - بيانات الفئة الفرعية للحفظ:', subcategoryData);
    const newSubcategory = await addSubcategory(subcategoryData);
    console.log('✅ API الإدارة - تم إنشاء الفئة الفرعية بنجاح:', newSubcategory);

    return NextResponse.json({
      success: true,
      data: newSubcategory,
      message: 'Subcategory created successfully',
      messageAr: 'تم إنشاء الفئة الفرعية بنجاح'
    }, { status: 201 });
  } catch (error) {
    console.error('❌ API الإدارة - خطأ في إضافة الفئة الفرعية:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي',
      details: error instanceof Error ? error.message : 'خطأ غير معروف'
    }, { status: 500 });
  }
}

// PUT - تحديث فئة فرعية
export async function PUT(request: NextRequest) {
  try {
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const { searchParams } = new URL(request.url);
    const updateId = searchParams.get('id');

    if (!updateId || typeof updateId !== 'string') {
      return NextResponse.json({
        success: false,
        message: 'Subcategory ID is required',
        messageAr: 'معرف الفئة الفرعية مطلوب'
      }, { status: 400 });
    }

    const existingSubcategory = await getSubcategoryById(updateId);
    if (!existingSubcategory) {
      return NextResponse.json({
        success: false,
        message: 'Subcategory not found',
        messageAr: 'الفئة الفرعية غير موجودة'
      }, { status: 404 });
    }

    const body = await request.json();
    const { name, nameAr, description, descriptionAr, image, isActive, categoryId } = body;

    // تحويل أسماء الحقول لتتطابق مع قاعدة البيانات
    const updateData = {
      name,
      name_ar: nameAr,
      description,
      description_ar: descriptionAr,
      image_url: image,
      is_active: isActive,
      category_id: categoryId
    };

    const updatedSubcategory = await updateSubcategory(updateId, updateData);
    return NextResponse.json({
      success: true,
      data: updatedSubcategory,
      message: 'Subcategory updated successfully',
      messageAr: 'تم تحديث الفئة الفرعية بنجاح'
    });
  } catch (error) {
    console.error('Admin Subcategories PUT API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}

// DELETE - حذف فئة فرعية
export async function DELETE(request: NextRequest) {
  try {
    // TODO: إعادة تفعيل المصادقة لاحقاً
    // const user = requireAdminAuth(request);
    // if (!user) {
    //   return NextResponse.json({
    //     success: false,
    //     message: 'Authentication required',
    //     messageAr: 'المصادقة مطلوبة'
    //   }, { status: 401 });
    // }

    const { searchParams } = new URL(request.url);
    const deleteId = searchParams.get('id');

    if (!deleteId || typeof deleteId !== 'string') {
      return NextResponse.json({
        success: false,
        message: 'Subcategory ID is required',
        messageAr: 'معرف الفئة الفرعية مطلوب'
      }, { status: 400 });
    }

    const deleted = await deleteSubcategory(deleteId);

    if (!deleted) {
      return NextResponse.json({
        success: false,
        message: 'Subcategory not found',
        messageAr: 'الفئة الفرعية غير موجودة'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'Subcategory deleted successfully',
      messageAr: 'تم حذف الفئة الفرعية بنجاح'
    });
  } catch (error) {
    console.error('Admin Subcategories DELETE API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      messageAr: 'خطأ في الخادم الداخلي'
    }, { status: 500 });
  }
}
