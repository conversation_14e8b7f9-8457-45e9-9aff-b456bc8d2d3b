import { Metadata } from 'next';
import { Locale } from '../../../../lib/i18n';

interface ProductLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string; id: string }>;
}

// دالة لجلب بيانات المنتج للـ SEO
async function getProductForSEO(id: string) {
  try {
    // استخدام localhost في بيئة التطوير
    const baseUrl = process.env.NODE_ENV === 'development'
      ? 'http://localhost:3000'
      : (process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com');

    const response = await fetch(`${baseUrl}/api/products/${id}`, {
      cache: 'no-store',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.log(`Product API returned ${response.status} for ID: ${id}`);
      return null;
    }

    const result = await response.json();
    return result.success ? result.data : null;
  } catch (error) {
    console.error('Error fetching product for SEO:', error);
    // إرجاع بيانات افتراضية بدلاً من null
    return {
      id: id,
      title: 'منتج',
      title_ar: 'منتج',
      description: 'وصف المنتج',
      description_ar: 'وصف المنتج',
      price: 0,
      images: []
    };
  }
}

// إنشاء metadata للمنتج
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string; id: string }>
}): Promise<Metadata> {
  const { locale: localeParam, id } = await params;
  const locale = (localeParam || 'ar') as Locale;
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  // جلب بيانات المنتج
  const product = await getProductForSEO(id);

  if (!product) {
    return {
      title: locale === 'ar' ? 'منتج غير موجود | دروب هجر' : 'Product Not Found | DROOB HAJER',
      description: locale === 'ar'
        ? 'عذراً، لم نتمكن من العثور على هذا المنتج'
        : 'Sorry, we could not find this product',
    };
  }

  const title = locale === 'ar' ? product.title_ar : product.title;
  const description = locale === 'ar' ? product.description_ar : product.description;
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';

  return {
    title: `${title} | ${siteName}`,
    description: description,
    keywords: locale === 'ar'
      ? `${title}, تجهيزات فندقية, أثاث فندقي, دروب هجر, معدات فندقية`
      : `${title}, hotel equipment, hotel furniture, DROOB HAJER, hotel supplies`,

    openGraph: {
      title: `${title} | ${siteName}`,
      description: description,
      url: `${baseUrl}/${locale}/product/${id}`,
      siteName: siteName,
      images: product.images?.[0]?.image_url ? [
        {
          url: product.images[0].image_url,
          width: 800,
          height: 600,
          alt: title,
        },
      ] : [],
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      type: 'website',
    },

    twitter: {
      card: 'summary_large_image',
      title: `${title} | ${siteName}`,
      description: description,
      images: product.images?.[0]?.image_url ? [product.images[0].image_url] : [],
    },

    alternates: {
      canonical: `${baseUrl}/${locale}/product/${id}`,
      languages: {
        'ar': `${baseUrl}/ar/product/${id}`,
        'en': `${baseUrl}/en/product/${id}`,
      },
    },
  };
}

export default async function ProductLayout({
  children
}: ProductLayoutProps) {
  return (
    <>
      {children}
    </>
  );
}
