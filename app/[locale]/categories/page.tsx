import { Metadata } from 'next';
import { Locale } from '../../../lib/i18n';
import { getTranslation } from '../../../lib/translations';
import CategoriesPage from '../../../components/CategoriesPage';

interface PageProps {
  params: Promise<{
    locale: Locale;
  }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale } = await params;
  const t = (key: string) => getTranslation(locale, key as keyof typeof import('../../../lib/translations').translations.ar);

  return {
    title: `${t('categories')} - DROOB HAJER`,
    description: t('categories_description') || 'تصفح جميع فئات المنتجات في متجر دروب هاجر',
  };
}

export default async function Categories({ params }: PageProps) {
  const { locale } = await params;

  return <CategoriesPage locale={locale} />;
}
