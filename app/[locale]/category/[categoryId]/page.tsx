'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import { Locale } from '../../../../lib/i18n';
import { getTranslation, TranslationKey, NestedTranslationKey } from '../../../../lib/translations';
import Navbar from '../../../../components/Navbar';
import Footer from '../../../../components/Footer';
import WhatsAppButton from '../../../../components/WhatsAppButton';
import { Category, Subcategory } from '../../../../types/mysql-database';

export default function CategoryPage() {
  const params = useParams();
  const router = useRouter();
  const locale = (params?.locale || 'ar') as Locale;
  const categoryId = params?.categoryId as string;
  
  const [category, setCategory] = useState<Category | null>(null);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [loading, setLoading] = useState(true);

  const t = (key: TranslationKey | NestedTranslationKey) => getTranslation(locale, key);

  const fetchCategoryData = useCallback(async () => {
    try {
      setLoading(true);

      // جلب بيانات الفئة
      const categoriesResponse = await fetch(`/api/categories?id=${categoryId}`);
      if (categoriesResponse.ok) {
        const categoriesResult = await categoriesResponse.json();
        console.log('📦 استجابة API الفئة:', categoriesResult);

        if (categoriesResult.success && categoriesResult.data) {
          setCategory(categoriesResult.data);
        } else {
          console.error('❌ فشل في جلب الفئة:', categoriesResult);
          router.push(`/${locale}/products`);
          return;
        }
      } else {
        router.push(`/${locale}/products`);
        return;
      }

      // جلب الفئات الفرعية
      const subcategoriesResponse = await fetch(`/api/subcategories?categoryId=${categoryId}`);
      console.log('📡 استجابة الفئات الفرعية - الحالة:', subcategoriesResponse.status, subcategoriesResponse.ok);

      if (subcategoriesResponse.ok) {
        const subcategoriesResult = await subcategoriesResponse.json();
        console.log('📦 استجابة API الفئات الفرعية:', subcategoriesResult);

        if (subcategoriesResult.success && subcategoriesResult.data) {
          // تصفية الفئات الفرعية النشطة فقط
          const activeSubcategories = subcategoriesResult.data.filter((sub: Subcategory) => sub.is_active);
          console.log('✅ الفئات الفرعية النشطة:', activeSubcategories.length);
          setSubcategories(activeSubcategories);
        } else {
          console.error('❌ فشل في جلب الفئات الفرعية:', subcategoriesResult);
          setSubcategories([]);
        }
      } else {
        console.error('❌ خطأ في استجابة API الفئات الفرعية - الحالة:', subcategoriesResponse.status);
        // محاولة قراءة رسالة الخطأ
        try {
          const errorResult = await subcategoriesResponse.json();
          console.error('❌ تفاصيل الخطأ:', errorResult);
        } catch {
          console.error('❌ لا يمكن قراءة تفاصيل الخطأ');
        }
        setSubcategories([]);
      }

    } catch (error) {
      console.error('❌ خطأ في جلب بيانات الفئة:', error);
      setCategory(null);
      setSubcategories([]);
    } finally {
      setLoading(false);
    }
  }, [categoryId, router, locale]);

  useEffect(() => {
    if (categoryId) {
      fetchCategoryData();
    }
  }, [categoryId, fetchCategoryData]);



  if (loading) {
    return (
      <>
        <Navbar locale={locale} />
        <main className="min-h-screen bg-gray-50 py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-16">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/20 border-t-primary mx-auto mb-6"></div>
              <p className="text-gray-700 text-lg font-medium">
                {locale === 'ar' ? 'جاري تحميل منتجات الفئة...' : 'Loading category products...'}
              </p>
            </div>
          </div>
        </main>
        <Footer locale={locale} />
        <WhatsAppButton locale={locale} />
      </>
    );
  }

  if (!category) {
    return (
      <>
        <Navbar locale={locale} />
        <main className="min-h-screen bg-gray-50 py-8">
          <div className="container mx-auto px-4">
            <div className="text-center py-16">
              <i className="ri-error-warning-line text-6xl text-red-400 mb-4"></i>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                {locale === 'ar' ? 'الفئة غير موجودة' : 'Category not found'}
              </h3>
              <p className="text-gray-600 mb-6">
                {locale === 'ar' 
                  ? 'الفئة التي تبحث عنها غير موجودة أو تم حذفها'
                  : 'The category you are looking for does not exist or has been deleted'
                }
              </p>
              <button
                onClick={() => router.push(`/${locale}/products`)}
                className="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300"
              >
                {locale === 'ar' ? 'العودة للمنتجات' : 'Back to Products'}
              </button>
            </div>
          </div>
        </main>
        <Footer locale={locale} />
        <WhatsAppButton locale={locale} />
      </>
    );
  }

  return (
    <>
      <Navbar locale={locale} />
      <main className="min-h-screen bg-gray-50">
        {/* Page Header */}
        <section className="bg-gradient-to-r from-blue-900 via-blue-800 to-blue-700 py-16">
          <div className="container mx-auto px-4">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 rtl:space-x-reverse text-sm text-white/80 mb-6">
              <a href={`/${locale}`} className="hover:text-white transition-colors">
                {t('home')}
              </a>
              <span>/</span>
              <a href={`/${locale}/products`} className="hover:text-white transition-colors">
                {t('products')}
              </a>
              <span>/</span>
              <span className="text-white font-medium">
                {locale === 'ar' ? category.name_ar : category.name}
              </span>
            </nav>
            
            <div className="text-center text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">
                {locale === 'ar' ? category.name_ar : category.name}
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto mb-6">
                {locale === 'ar' ? category.description_ar : category.description}
              </p>
              <div className="flex items-center justify-center space-x-4 rtl:space-x-reverse text-white/80">
                <span className="flex items-center space-x-2 rtl:space-x-reverse">
                  <i className="ri-folder-line"></i>
                  <span>{subcategories.length} {locale === 'ar' ? 'فئة فرعية' : 'Subcategories'}</span>
                </span>
              </div>
            </div>
          </div>
        </section>

        {/* Subcategories Section */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                {locale === 'ar' ? 'الفئات الفرعية' : 'Subcategories'}
              </h2>
              <p className="text-gray-600 text-lg max-w-2xl mx-auto">
                {locale === 'ar'
                  ? 'اختر الفئة الفرعية لعرض المنتجات المتعلقة بها'
                  : 'Choose a subcategory to view related products'
                }
              </p>
            </div>

            {/* Subcategories Grid */}
            {subcategories.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                {subcategories.map((subcategory) => (
                  <div
                    key={subcategory.id}
                    onClick={() => router.push(`/${locale}/subcategory/${subcategory.id}`)}
                    className="group bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer"
                  >
                    <div className="aspect-square bg-gray-200 overflow-hidden relative">
                      {subcategory.image_url ? (
                        <Image
                          src={subcategory.image_url}
                          alt={locale === 'ar' ? subcategory.name_ar : subcategory.name}
                          fill
                          className="object-cover group-hover:scale-110 transition-transform duration-300"
                          sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw"
                          onError={(e) => {
                            (e.target as HTMLImageElement).src = '/api/placeholder?width=400&height=300&text=صورة الفئة الفرعية';
                          }}
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary/10 to-secondary/10">
                          <i className="ri-folder-2-line text-4xl text-primary"></i>
                        </div>
                      )}
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-800 mb-2 group-hover:text-primary transition-colors duration-300">
                        {locale === 'ar' ? subcategory.name_ar : subcategory.name}
                      </h3>
                      {subcategory.description_ar && (
                        <p className="text-gray-600 text-sm line-clamp-2">
                          {locale === 'ar' ? subcategory.description_ar : subcategory.description}
                        </p>
                      )}
                      <div className="mt-4 flex items-center justify-between">
                        <span className="text-sm text-gray-500">
                          {locale === 'ar' ? 'عرض المنتجات' : 'View Products'}
                        </span>
                        <div className="bg-primary/10 rounded-full p-2 group-hover:bg-primary group-hover:text-white transition-all duration-300">
                          <i className="ri-arrow-right-line text-primary group-hover:text-white"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-16 bg-white rounded-2xl shadow-lg">
                <i className="ri-folder-line text-6xl text-gray-400 mb-4"></i>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  {locale === 'ar' ? 'لا توجد فئات فرعية' : 'No subcategories found'}
                </h3>
                <p className="text-gray-600 mb-6">
                  {locale === 'ar'
                    ? 'هذه الفئة لا تحتوي على فئات فرعية. يمكنك العودة لعرض جميع المنتجات.'
                    : 'This category has no subcategories. You can go back to view all products.'
                  }
                </p>
                <button
                  onClick={() => router.push(`/${locale}/products?category=${categoryId}`)}
                  className="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300"
                >
                  {locale === 'ar' ? 'عرض منتجات هذه الفئة' : 'View Category Products'}
                </button>
              </div>
            )}

            {/* Back to Home */}
            <div className="mt-12 text-center">
              <button
                onClick={() => router.push(`/${locale}`)}
                className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-8 py-4 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 flex items-center justify-center mx-auto space-x-2 rtl:space-x-reverse"
              >
                <i className="ri-arrow-left-line"></i>
                <span>{locale === 'ar' ? 'العودة للصفحة الرئيسية' : 'Back to Home'}</span>
              </button>
            </div>
          </div>
        </section>
      </main>
      <Footer locale={locale} />
      <WhatsAppButton locale={locale} />
    </>
  );
}
