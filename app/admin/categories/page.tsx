
'use client';

import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import AdminLayout from '../../../components/admin/AdminLayout';
import ImageUploadWithURL from '../../../components/ImageUploadWithURL';
import SafeImage from '../../../components/SafeImage';
import { Category } from '../../../types/mysql-database';

const CategoriesAdmin = () => {
  const [categoriesList, setCategoriesList] = useState<Category[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const [formData, setFormData] = useState({
    name: '',
    nameAr: '',
    description: '',
    descriptionAr: '',
    image: '',
    isActive: true
  });

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async (forceRefresh = false) => {
    try {
      setLoading(true);
      console.log('🔄 جلب الفئات من API الإدارة...', forceRefresh ? '(إجبار التحديث)' : '');

      // إضافة timestamp لمنع cache عند التحديث
      const timestamp = forceRefresh ? `?t=${Date.now()}` : '';
      const response = await fetch(`/api/admin/categories${timestamp}`, {
        cache: forceRefresh ? 'no-store' : 'default',
        headers: {
          'Cache-Control': forceRefresh ? 'no-cache' : 'default'
        }
      });
      console.log('📡 استجابة API الإدارة:', response.status, response.ok);

      if (response.ok) {
        const result = await response.json();
        console.log('📦 بيانات الفئات من API الإدارة:', result);
        setCategoriesList(result.data || []);
        console.log('✅ تم تحديث قائمة الفئات في الواجهة');
      } else {
        const errorResult = await response.json();
        console.error('❌ خطأ في API الإدارة:', errorResult);
        throw new Error('Failed to fetch categories');
      }
    } catch (error) {
      console.error('❌ خطأ في تحميل الفئات:', error);
      setError('حدث خطأ أثناء تحميل الفئات');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError('');

      const requestData = {
        name: formData.name,
        nameAr: formData.nameAr,
        description: formData.description,
        descriptionAr: formData.descriptionAr,
        image: formData.image,
        isActive: formData.isActive
      };

      console.log('📤 إرسال بيانات الفئة:', requestData);

      if (editingCategory) {
        // تحديث فئة موجودة
        console.log('🔄 تحديث فئة موجودة - ID:', editingCategory.id);
        const response = await fetch(`/api/admin/categories?id=${editingCategory.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData),
        });

        console.log('📡 استجابة تحديث الفئة:', response.status, response.ok);
        const result = await response.json();
        console.log('📦 نتيجة تحديث الفئة:', result);

        if (!response.ok) {
          throw new Error(result.messageAr || result.message || 'Failed to update category');
        }

        console.log('✅ تم تحديث الفئة بنجاح:', result);

        // تحديث فوري للحالة المحلية
        if (result.data) {
          setCategoriesList(prev =>
            prev.map(cat =>
              cat.id === editingCategory.id
                ? result.data
                : cat
            )
          );
          console.log('✅ تم تحديث الفئة في الواجهة فوراً');
        }

        // إرسال إشعار للمكونات الأخرى
        localStorage.setItem('categories_updated', Date.now().toString());
        window.dispatchEvent(new CustomEvent('categories_updated', {
          detail: { type: 'categories_updated', action: 'update', data: result.data }
        }));
      } else {
        // إضافة فئة جديدة
        console.log('➕ إضافة فئة جديدة');
        const response = await fetch('/api/admin/categories', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData),
        });

        console.log('📡 استجابة إضافة الفئة:', response.status, response.ok);
        const result = await response.json();
        console.log('📦 نتيجة إضافة الفئة:', result);

        if (!response.ok) {
          throw new Error(result.messageAr || result.message || 'Failed to create category');
        }

        console.log('✅ تم إنشاء الفئة بنجاح:', result);

        // تحديث فوري للحالة المحلية
        if (result.data) {
          setCategoriesList(prev => [...prev, result.data]);
          console.log('✅ تم إضافة الفئة للواجهة فوراً');
        }

        // إرسال إشعار للمكونات الأخرى
        localStorage.setItem('categories_updated', Date.now().toString());
        window.dispatchEvent(new CustomEvent('categories_updated', {
          detail: { type: 'categories_updated', action: 'create', data: result.data }
        }));
      }

      // تحديث قائمة الفئات من الخادم للتأكد
      console.log('🔄 تحديث قائمة الفئات من الخادم...');
      await loadCategories(true);
      resetForm();
      console.log('✅ تم تحديث الواجهة بنجاح');
    } catch (error) {
      console.error('❌ خطأ في حفظ الفئة:', error);
      setError(`حدث خطأ أثناء حفظ الفئة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      nameAr: '',
      description: '',
      descriptionAr: '',
      image: '',
      isActive: true
    });
    setEditingCategory(null);
    setShowModal(false);
  };

  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    setFormData({
      name: category.name,
      nameAr: category.name_ar,
      description: category.description || '',
      descriptionAr: category.description_ar || '',
      image: category.image_url || '',
      isActive: category.is_active
    });
    setShowModal(true);
  };

  const handleDelete = async (categoryId: string) => {
    if (confirm('هل أنت متأكد من حذف هذه الفئة؟ سيتم حذف جميع الفئات الفرعية والمنتجات المرتبطة بها.')) {
      try {
        setLoading(true);
        console.log('🗑️ حذف فئة - ID:', categoryId);
        const response = await fetch(`/api/admin/categories?id=${categoryId}`, {
          method: 'DELETE',
        });

        console.log('📡 استجابة حذف الفئة:', response.status, response.ok);
        const result = await response.json();
        console.log('📦 نتيجة حذف الفئة:', result);

        if (!response.ok) {
          throw new Error(result.messageAr || result.message || 'Failed to delete category');
        }

        console.log('✅ تم حذف الفئة بنجاح');

        // تحديث فوري للحالة المحلية - إزالة الفئة من القائمة
        setCategoriesList(prev => prev.filter(cat => cat.id !== categoryId));
        console.log('✅ تم إزالة الفئة من الواجهة فوراً');

        // إرسال إشعار للمكونات الأخرى
        localStorage.setItem('categories_updated', Date.now().toString());
        window.dispatchEvent(new CustomEvent('categories_updated', {
          detail: { type: 'categories_updated', action: 'delete', categoryId }
        }));

        // تحديث قائمة الفئات من الخادم للتأكد
        console.log('🔄 تحديث قائمة الفئات من الخادم...');
        await loadCategories(true);
        console.log('✅ تم تحديث الواجهة بعد الحذف');
      } catch (error) {
        console.error('❌ خطأ في حذف الفئة:', error);
        setError(`حدث خطأ أثناء حذف الفئة: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`);
      } finally {
        setLoading(false);
      }
    }
  };

  const toggleStatus = async (categoryId: string) => {
    try {
      const category = categoriesList.find(cat => cat.id === categoryId);
      if (category) {
        const response = await fetch(`/api/admin/categories?id=${categoryId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            isActive: !category.is_active
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to update category status');
        }

        // تحديث فوري للحالة المحلية
        setCategoriesList(prev =>
          prev.map(cat =>
            cat.id === categoryId
              ? { ...cat, is_active: !cat.is_active }
              : cat
          )
        );
        console.log('✅ تم تحديث حالة الفئة في الواجهة فوراً');

        // إرسال إشعار للمكونات الأخرى
        localStorage.setItem('categories_updated', Date.now().toString());
        window.dispatchEvent(new CustomEvent('categories_updated', {
          detail: { type: 'categories_updated', action: 'toggle_status', categoryId }
        }));

        // تحديث قائمة الفئات من الخادم للتأكد
        console.log('🔄 تحديث قائمة الفئات من الخادم...');
        await loadCategories(true);
        console.log('✅ تم تحديث الواجهة بعد تغيير الحالة');
      }
    } catch (error) {
      console.error('Error updating category status:', error);
      setError('حدث خطأ أثناء تحديث حالة الفئة');
    }
  };

  return (
    <div>
      <Head>
        <title>إدارة الفئات الرئيسية - لوحة التحكم</title>
        <meta name="description" content="إدارة الفئات الرئيسية للمنتجات" />
      </Head>

      <AdminLayout title="الفئات الرئيسية">
        <div className="space-y-6">
          {error && (
            <div className="bg-gradient-to-r from-red-50 to-red-100 border border-red-200 text-red-700 px-4 py-3 rounded-xl flex items-center shadow-lg">
              <i className="ri-error-warning-line text-lg ml-2"></i>
              <span>{error}</span>
              <button
                onClick={() => setError('')}
                className="mr-auto text-red-600 hover:text-red-800"
              >
                <i className="ri-close-line text-lg"></i>
              </button>
            </div>
          )}

          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-800">الفئات الرئيسية</h1>
              <p className="text-gray-600">إدارة فئات المنتجات الرئيسية</p>
            </div>
            <button
              onClick={() => setShowModal(true)}
              disabled={loading}
              className="bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 disabled:opacity-50 text-white px-6 py-3 rounded-xl flex items-center transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
              ) : (
                <i className="ri-add-line text-lg ml-2"></i>
              )}
              إضافة فئة جديدة
            </button>
          </div>

          {loading && categoriesList.length === 0 ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">جاري تحميل الفئات...</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {categoriesList.map((category) => (
                <div key={category.id} className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                  {category.image_url && (
                    <div className="h-48 bg-gray-200 overflow-hidden">
                      <SafeImage
                        src={category.image_url}
                        alt={category.name_ar}
                        width={400}
                        height={300}
                        className="w-full h-full object-cover"
                        fallbackText="صورة الفئة"
                      />
                    </div>
                  )}
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-800 mb-1">{category.name_ar}</h3>
                        <p className="text-sm text-gray-500">{category.name}</p>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          category.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {category.is_active ? 'نشط' : 'غير نشط'}
                        </span>
                      </div>
                    </div>

                    {category.description && (
                      <p className="text-sm text-gray-600 mb-4">{category.description_ar || category.description}</p>
                    )}

                    <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => handleEdit(category)}
                          className="text-blue-600 hover:text-blue-700 p-2 hover:bg-blue-50 rounded-lg transition-colors duration-200"
                          title="تعديل"
                        >
                          <i className="ri-edit-line text-lg"></i>
                        </button>
                        <button
                          onClick={() => toggleStatus(category.id)}
                          className={`p-2 rounded-lg transition-colors duration-200 ${
                            category.is_active
                              ? 'text-red-600 hover:text-red-700 hover:bg-red-50'
                              : 'text-green-600 hover:text-green-700 hover:bg-green-50'
                          }`}
                          title={category.is_active ? 'إلغاء التفعيل' : 'تفعيل'}
                        >
                          <i className={`ri-${category.is_active ? 'eye-off' : 'eye'}-line text-lg`}></i>
                        </button>
                        <button
                          onClick={() => handleDelete(category.id)}
                          className="text-red-600 hover:text-red-700 p-2 hover:bg-red-50 rounded-lg transition-colors duration-200"
                          title="حذف"
                        >
                          <i className="ri-delete-bin-line text-lg"></i>
                        </button>
                      </div>
                      <div className="text-xs text-gray-400">
                        {new Date(category.updated_at).toLocaleDateString('ar-SA')}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {!loading && categoriesList.length === 0 && (
            <div className="text-center py-12">
              <i className="ri-folder-line text-6xl text-gray-400 mb-4"></i>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد فئات</h3>
              <p className="text-gray-600 mb-4">ابدأ بإضافة فئة رئيسية جديدة</p>
              <button
                onClick={() => setShowModal(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg inline-flex items-center transition-colors duration-200"
              >
                <i className="ri-add-line text-lg ml-2"></i>
                إضافة فئة جديدة
              </button>
            </div>
          )}
        </div>

        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-gray-800">
                    {editingCategory ? 'تعديل الفئة' : 'إضافة فئة جديدة'}
                  </h2>
                  <button
                    onClick={resetForm}
                    className="text-gray-400 hover:text-gray-600 p-2"
                  >
                    <i className="ri-close-line text-xl"></i>
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الاسم بالعربية *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.nameAr}
                      onChange={(e) => setFormData({...formData, nameAr: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="أدخل اسم الفئة بالعربية"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الاسم بالإنجليزية *
                    </label>
                    <input
                      type="text"
                      required
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter category name in English"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوصف بالعربية
                    </label>
                    <textarea
                      value={formData.descriptionAr}
                      onChange={(e) => setFormData({...formData, descriptionAr: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={3}
                      placeholder="أدخل وصف الفئة بالعربية"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوصف بالإنجليزية
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={3}
                      placeholder="Enter category description in English"
                    />
                  </div>

                  <ImageUploadWithURL
                    onImagesUploaded={(images: string[]) => {
                      const validImage = images.find(img => img && img.trim()) || '';
                      setFormData({...formData, image: validImage});
                    }}
                    multiple={false}
                    currentImages={formData.image && formData.image.trim() ? [formData.image] : []}
                    label="صورة الفئة"
                  />

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isActive"
                      checked={formData.isActive}
                      onChange={(e) => setFormData({...formData, isActive: e.target.checked})}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <label htmlFor="isActive" className="mr-2 text-sm font-medium text-gray-700">
                      فئة نشطة
                    </label>
                  </div>

                  <div className="flex space-x-3 space-x-reverse pt-4">
                    <button
                      type="submit"
                      disabled={loading}
                      className="flex-1 bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 disabled:opacity-50 text-white py-3 px-4 rounded-xl font-bold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                    >
                      {loading ? (
                        <div className="flex items-center justify-center">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white ml-2"></div>
                          جاري الحفظ...
                        </div>
                      ) : (
                        editingCategory ? 'تحديث الفئة' : 'إضافة الفئة'
                      )}
                    </button>
                    <button
                      type="button"
                      onClick={resetForm}
                      disabled={loading}
                      className="flex-1 bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 disabled:opacity-50 text-gray-800 py-3 px-4 rounded-xl font-bold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                    >
                      إلغاء
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </AdminLayout>
    </div>
  );
};

export default CategoriesAdmin;
