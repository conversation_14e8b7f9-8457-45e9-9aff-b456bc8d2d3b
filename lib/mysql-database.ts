import { executeQuery, executeQuerySingle, executeInsert, executeUpdate } from './database-config';
import { 
  Category, 
  Subcategory, 
  Product, 
  ProductImage, 
  ProductFeature, 
  ProductSpecification,
  QuoteRequest,
  QuoteRequestProduct,
  QuoteRequestLog,
  Admin,
  ContactInfo,
  CategoryInput,
  SubcategoryInput,
  ProductInput,
  QuoteRequestInput,
  AdminInput,
  CategoryUpdate,
  SubcategoryUpdate,
  ProductUpdate,
  QuoteRequestUpdate,
  AdminUpdate,
  ProductWithDetails,
  QuoteRequestWithDetails
} from '../types/mysql-database';

// ==================== دوال الفئات الرئيسية ====================

// الحصول على جميع الفئات النشطة
export async function getCategories(): Promise<Category[]> {
  const query = `
    SELECT * FROM categories 
    WHERE deleted_at IS NULL AND is_active = 1 
    ORDER BY created_at DESC
  `;
  return await executeQuery<Category>(query);
}

// الحصول على فئة بواسطة ID
export async function getCategoryById(id: string): Promise<Category | null> {
  const query = `
    SELECT * FROM categories 
    WHERE id = ? AND deleted_at IS NULL
  `;
  return await executeQuerySingle<Category>(query, [id]);
}

// إضافة فئة جديدة
export async function addCategory(category: CategoryInput): Promise<Category> {
  console.log('📥 قاعدة البيانات - إضافة فئة جديدة:', category);

  const query = `
    INSERT INTO categories (id, name, name_ar, description, description_ar, image_url, is_active)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `;

  console.log('📤 قاعدة البيانات - تنفيذ الاستعلام:', query);
  console.log('📤 قاعدة البيانات - القيم:', [
    category.id,
    category.name,
    category.name_ar,
    category.description || null,
    category.description_ar || null,
    category.image_url || null,
    category.is_active
  ]);

  await executeInsert(query, [
    category.id,
    category.name,
    category.name_ar,
    category.description || null,
    category.description_ar || null,
    category.image_url || null,
    category.is_active
  ]);

  console.log('✅ قاعدة البيانات - تم إدراج الفئة، جلب البيانات...');
  const newCategory = await getCategoryById(category.id);
  if (!newCategory) {
    console.error('❌ قاعدة البيانات - فشل في إنشاء الفئة');
    throw new Error('Failed to create category');
  }
  console.log('✅ قاعدة البيانات - تم إنشاء الفئة بنجاح:', newCategory);
  return newCategory;
}

// تحديث فئة
export async function updateCategory(id: string, updates: CategoryUpdate): Promise<Category> {
  console.log('🔄 تحديث الفئة - ID:', id);
  console.log('🔄 البيانات المرسلة:', updates);

  const setClause = [];
  const values = [];

  if (updates.name !== undefined) {
    setClause.push('name = ?');
    values.push(updates.name);
  }
  if (updates.name_ar !== undefined) {
    setClause.push('name_ar = ?');
    values.push(updates.name_ar);
  }
  if (updates.description !== undefined) {
    setClause.push('description = ?');
    values.push(updates.description);
  }
  if (updates.description_ar !== undefined) {
    setClause.push('description_ar = ?');
    values.push(updates.description_ar);
  }
  if (updates.image_url !== undefined) {
    setClause.push('image_url = ?');
    values.push(updates.image_url);
  }
  if (updates.is_active !== undefined) {
    setClause.push('is_active = ?');
    values.push(updates.is_active);
  }

  setClause.push('updated_at = CURRENT_TIMESTAMP');
  values.push(id);

  const query = `
    UPDATE categories
    SET ${setClause.join(', ')}
    WHERE id = ? AND deleted_at IS NULL
  `;

  console.log('🔄 استعلام SQL:', query);
  console.log('🔄 القيم:', values);

  const result = await executeUpdate(query, values);
  console.log('🔄 نتيجة التحديث:', result);

  if (result.affectedRows === 0) {
    console.error('❌ لم يتم العثور على الفئة أو لم يتم إجراء تغييرات');
    throw new Error('Category not found or no changes made');
  }

  const updatedCategory = await getCategoryById(id);
  if (!updatedCategory) {
    console.error('❌ فشل في استرداد الفئة المحدثة');
    throw new Error('Failed to retrieve updated category');
  }

  console.log('✅ تم تحديث الفئة بنجاح:', updatedCategory);
  return updatedCategory;
}

// حذف فئة (hard delete - حذف فعلي من قاعدة البيانات)
export async function deleteCategory(id: string): Promise<boolean> {
  console.log('📥 قاعدة البيانات - حذف فئة نهائياً - ID:', id);

  try {
    // أولاً: حذف جميع الفئات الفرعية المرتبطة بهذه الفئة
    const deleteSubcategoriesQuery = `
      DELETE FROM subcategories
      WHERE category_id = ?
    `;
    console.log('🗑️ قاعدة البيانات - حذف الفئات الفرعية المرتبطة...');
    await executeUpdate(deleteSubcategoriesQuery, [id]);

    // ثانياً: حذف جميع المنتجات المرتبطة بهذه الفئة
    const deleteProductsQuery = `
      DELETE FROM products
      WHERE category_id = ?
    `;
    console.log('🗑️ قاعدة البيانات - حذف المنتجات المرتبطة...');
    await executeUpdate(deleteProductsQuery, [id]);

    // ثالثاً: حذف الفئة نفسها
    const deleteCategoryQuery = `
      DELETE FROM categories
      WHERE id = ?
    `;
    console.log('🗑️ قاعدة البيانات - حذف الفئة الرئيسية...');
    const result = await executeUpdate(deleteCategoryQuery, [id]);
    console.log('📦 قاعدة البيانات - نتيجة حذف الفئة:', result);

    const success = result.affectedRows > 0;
    console.log(success ? '✅ قاعدة البيانات - تم حذف الفئة نهائياً بنجاح' : '❌ قاعدة البيانات - لم يتم حذف أي فئة');
    return success;

  } catch (error) {
    console.error('❌ قاعدة البيانات - خطأ في حذف الفئة:', error);
    throw error;
  }
}

// ==================== دوال الفئات الفرعية ====================

// الحصول على جميع الفئات الفرعية
export async function getSubcategories(): Promise<Subcategory[]> {
  const query = `
    SELECT * FROM subcategories 
    WHERE deleted_at IS NULL AND is_active = 1 
    ORDER BY created_at DESC
  `;
  return await executeQuery<Subcategory>(query);
}

// الحصول على الفئات الفرعية لفئة معينة
export async function getSubcategoriesByCategory(categoryId: string): Promise<Subcategory[]> {
  const query = `
    SELECT * FROM subcategories 
    WHERE category_id = ? AND deleted_at IS NULL AND is_active = 1 
    ORDER BY created_at DESC
  `;
  return await executeQuery<Subcategory>(query, [categoryId]);
}

// الحصول على فئة فرعية بواسطة ID
export async function getSubcategoryById(id: string): Promise<Subcategory | null> {
  const query = `
    SELECT * FROM subcategories 
    WHERE id = ? AND deleted_at IS NULL
  `;
  return await executeQuerySingle<Subcategory>(query, [id]);
}

// إضافة فئة فرعية جديدة
export async function addSubcategory(subcategory: SubcategoryInput): Promise<Subcategory> {
  console.log('📥 قاعدة البيانات - إضافة فئة فرعية جديدة:', subcategory);

  const query = `
    INSERT INTO subcategories (id, name, name_ar, category_id, description, description_ar, image_url, is_active)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `;

  console.log('📤 قاعدة البيانات - تنفيذ الاستعلام:', query);
  console.log('📤 قاعدة البيانات - القيم:', [
    subcategory.id,
    subcategory.name,
    subcategory.name_ar,
    subcategory.category_id,
    subcategory.description || null,
    subcategory.description_ar || null,
    subcategory.image_url || null,
    subcategory.is_active
  ]);

  await executeInsert(query, [
    subcategory.id,
    subcategory.name,
    subcategory.name_ar,
    subcategory.category_id,
    subcategory.description || null,
    subcategory.description_ar || null,
    subcategory.image_url || null,
    subcategory.is_active
  ]);

  console.log('✅ قاعدة البيانات - تم إدراج الفئة الفرعية، جلب البيانات...');
  const newSubcategory = await getSubcategoryById(subcategory.id);
  if (!newSubcategory) {
    console.error('❌ قاعدة البيانات - فشل في إنشاء الفئة الفرعية');
    throw new Error('Failed to create subcategory');
  }
  console.log('✅ قاعدة البيانات - تم إنشاء الفئة الفرعية بنجاح:', newSubcategory);
  return newSubcategory;
}

// تحديث فئة فرعية
export async function updateSubcategory(id: string, updates: SubcategoryUpdate): Promise<Subcategory> {
  console.log('🔄 تحديث الفئة الفرعية - ID:', id);
  console.log('🔄 البيانات المرسلة:', updates);

  const setClause = [];
  const values = [];

  if (updates.name !== undefined) {
    setClause.push('name = ?');
    values.push(updates.name);
  }
  if (updates.name_ar !== undefined) {
    setClause.push('name_ar = ?');
    values.push(updates.name_ar);
  }
  if (updates.category_id !== undefined) {
    setClause.push('category_id = ?');
    values.push(updates.category_id);
  }
  if (updates.description !== undefined) {
    setClause.push('description = ?');
    values.push(updates.description);
  }
  if (updates.description_ar !== undefined) {
    setClause.push('description_ar = ?');
    values.push(updates.description_ar);
  }
  if (updates.image_url !== undefined) {
    setClause.push('image_url = ?');
    values.push(updates.image_url);
  }
  if (updates.is_active !== undefined) {
    setClause.push('is_active = ?');
    values.push(updates.is_active);
  }

  setClause.push('updated_at = CURRENT_TIMESTAMP');
  values.push(id);

  const query = `
    UPDATE subcategories 
    SET ${setClause.join(', ')}
    WHERE id = ? AND deleted_at IS NULL
  `;

  console.log('🔄 استعلام SQL:', query);
  console.log('🔄 القيم:', values);

  const result = await executeUpdate(query, values);
  console.log('🔄 نتيجة التحديث:', result);

  if (result.affectedRows === 0) {
    console.error('❌ لم يتم العثور على الفئة الفرعية أو لم يتم إجراء تغييرات');
    throw new Error('Subcategory not found or no changes made');
  }

  const updatedSubcategory = await getSubcategoryById(id);
  if (!updatedSubcategory) {
    throw new Error('Failed to retrieve updated subcategory');
  }
  return updatedSubcategory;
}

// حذف فئة فرعية (hard delete - حذف فعلي من قاعدة البيانات)
export async function deleteSubcategory(id: string): Promise<boolean> {
  console.log('📥 قاعدة البيانات - حذف فئة فرعية نهائياً - ID:', id);

  try {
    // أولاً: حذف جميع المنتجات المرتبطة بهذه الفئة الفرعية
    const deleteProductsQuery = `
      DELETE FROM products
      WHERE subcategory_id = ?
    `;
    console.log('🗑️ قاعدة البيانات - حذف المنتجات المرتبطة بالفئة الفرعية...');
    await executeUpdate(deleteProductsQuery, [id]);

    // ثانياً: حذف الفئة الفرعية نفسها
    const deleteSubcategoryQuery = `
      DELETE FROM subcategories
      WHERE id = ?
    `;
    console.log('🗑️ قاعدة البيانات - حذف الفئة الفرعية...');
    const result = await executeUpdate(deleteSubcategoryQuery, [id]);
    console.log('📦 قاعدة البيانات - نتيجة حذف الفئة الفرعية:', result);

    const success = result.affectedRows > 0;
    console.log(success ? '✅ قاعدة البيانات - تم حذف الفئة الفرعية نهائياً بنجاح' : '❌ قاعدة البيانات - لم يتم حذف أي فئة فرعية');
    return success;

  } catch (error) {
    console.error('❌ قاعدة البيانات - خطأ في حذف الفئة الفرعية:', error);
    throw error;
  }
}

// ==================== دوال المنتجات ====================

// الحصول على جميع المنتجات
export async function getProducts(): Promise<Product[]> {
  const query = `
    SELECT * FROM products
    WHERE deleted_at IS NULL AND is_active = 1
    ORDER BY created_at DESC
  `;
  return await executeQuery<Product>(query);
}

// الحصول على المنتجات بواسطة الفئة (بدون تفاصيل)
export async function getProductsByCategory(categoryId: string): Promise<Product[]> {
  const query = `
    SELECT * FROM products
    WHERE category_id = ? AND deleted_at IS NULL AND is_active = 1
    ORDER BY created_at DESC
  `;
  return await executeQuery<Product>(query, [categoryId]);
}

// الحصول على المنتجات بواسطة الفئة الفرعية (بدون تفاصيل)
export async function getProductsBySubcategory(subcategoryId: string): Promise<Product[]> {
  const query = `
    SELECT * FROM products
    WHERE subcategory_id = ? AND deleted_at IS NULL AND is_active = 1
    ORDER BY created_at DESC
  `;
  return await executeQuery<Product>(query, [subcategoryId]);
}

// الحصول على المنتجات بواسطة الفئة مع التفاصيل (الصور والمميزات والمواصفات)
export async function getProductsByCategoryWithDetails(categoryId: string): Promise<ProductWithDetails[]> {
  try {
    const products = await getProductsByCategory(categoryId);
    const productsWithDetails: ProductWithDetails[] = [];

    for (const product of products) {
      const [images, features, specifications] = await Promise.all([
        getProductImages(product.id),
        getProductFeatures(product.id),
        getProductSpecifications(product.id)
      ]);

      productsWithDetails.push({
        ...product,
        images,
        features,
        specifications
      });
    }

    return productsWithDetails;
  } catch (error) {
    console.error('Error fetching products by category with details:', error);
    throw error;
  }
}

// الحصول على المنتجات بواسطة الفئة الفرعية مع التفاصيل (الصور والمميزات والمواصفات)
export async function getProductsBySubcategoryWithDetails(subcategoryId: string): Promise<ProductWithDetails[]> {
  try {
    const products = await getProductsBySubcategory(subcategoryId);
    const productsWithDetails: ProductWithDetails[] = [];

    for (const product of products) {
      const [images, features, specifications] = await Promise.all([
        getProductImages(product.id),
        getProductFeatures(product.id),
        getProductSpecifications(product.id)
      ]);

      productsWithDetails.push({
        ...product,
        images,
        features,
        specifications
      });
    }

    return productsWithDetails;
  } catch (error) {
    console.error('Error fetching products by subcategory with details:', error);
    throw error;
  }
}

// الحصول على المنتجات المميزة
export async function getFeaturedProducts(): Promise<Product[]> {
  const query = `
    SELECT * FROM products
    WHERE is_featured = 1 AND deleted_at IS NULL AND is_active = 1
    ORDER BY created_at DESC
  `;
  return await executeQuery<Product>(query);
}

// الحصول على المنتجات المميزة مع التفاصيل (الصور والمميزات والمواصفات)
export async function getFeaturedProductsWithDetails(): Promise<ProductWithDetails[]> {
  try {
    const products = await getFeaturedProducts();
    const productsWithDetails: ProductWithDetails[] = [];

    for (const product of products) {
      const [images, features, specifications] = await Promise.all([
        getProductImages(product.id),
        getProductFeatures(product.id),
        getProductSpecifications(product.id)
      ]);

      productsWithDetails.push({
        ...product,
        images,
        features,
        specifications
      });
    }

    return productsWithDetails;
  } catch (error) {
    console.error('Error fetching featured products with details:', error);
    throw error;
  }
}

// الحصول على منتج بواسطة ID
export async function getProductById(id: string): Promise<Product | null> {
  const query = `
    SELECT * FROM products
    WHERE id = ? AND deleted_at IS NULL
  `;
  return await executeQuerySingle<Product>(query, [id]);
}

// الحصول على منتج مع جميع التفاصيل
export async function getProductWithDetails(id: string): Promise<ProductWithDetails | null> {
  const product = await getProductById(id);
  if (!product) return null;

  const [images, features, specifications] = await Promise.all([
    getProductImages(id),
    getProductFeatures(id),
    getProductSpecifications(id)
  ]);

  return {
    ...product,
    images,
    features,
    specifications
  };
}

// إضافة منتج جديد
export async function addProduct(product: ProductInput): Promise<Product> {
  const query = `
    INSERT INTO products (
      id, title, title_ar, description, description_ar, price, original_price,
      is_available, category_id, subcategory_id, is_active, is_featured
    )
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  await executeInsert(query, [
    product.id,
    product.title,
    product.title_ar,
    product.description || null,
    product.description_ar || null,
    product.price,
    product.original_price || null,
    product.is_available,
    product.category_id,
    product.subcategory_id,
    product.is_active,
    product.is_featured
  ]);

  const newProduct = await getProductById(product.id);
  if (!newProduct) {
    throw new Error('Failed to create product');
  }
  return newProduct;
}

// تحديث منتج
export async function updateProduct(id: string, updates: ProductUpdate): Promise<Product> {
  const setClause = [];
  const values = [];

  if (updates.title !== undefined) {
    setClause.push('title = ?');
    values.push(updates.title);
  }
  if (updates.title_ar !== undefined) {
    setClause.push('title_ar = ?');
    values.push(updates.title_ar);
  }
  if (updates.description !== undefined) {
    setClause.push('description = ?');
    values.push(updates.description);
  }
  if (updates.description_ar !== undefined) {
    setClause.push('description_ar = ?');
    values.push(updates.description_ar);
  }
  if (updates.price !== undefined) {
    setClause.push('price = ?');
    values.push(updates.price);
  }
  if (updates.original_price !== undefined) {
    setClause.push('original_price = ?');
    values.push(updates.original_price);
  }
  if (updates.is_available !== undefined) {
    setClause.push('is_available = ?');
    values.push(updates.is_available);
  }
  if (updates.category_id !== undefined) {
    setClause.push('category_id = ?');
    values.push(updates.category_id);
  }
  if (updates.subcategory_id !== undefined) {
    setClause.push('subcategory_id = ?');
    values.push(updates.subcategory_id);
  }
  if (updates.is_active !== undefined) {
    setClause.push('is_active = ?');
    values.push(updates.is_active);
  }
  if (updates.is_featured !== undefined) {
    setClause.push('is_featured = ?');
    values.push(updates.is_featured);
  }

  setClause.push('updated_at = CURRENT_TIMESTAMP');
  values.push(id);

  const query = `
    UPDATE products
    SET ${setClause.join(', ')}
    WHERE id = ? AND deleted_at IS NULL
  `;

  const result = await executeUpdate(query, values);
  if (result.affectedRows === 0) {
    throw new Error('Product not found or no changes made');
  }

  const updatedProduct = await getProductById(id);
  if (!updatedProduct) {
    throw new Error('Failed to retrieve updated product');
  }
  return updatedProduct;
}

// حذف منتج (hard delete - حذف فعلي من قاعدة البيانات)
export async function deleteProduct(id: string): Promise<boolean> {
  console.log('📥 قاعدة البيانات - حذف منتج نهائياً - ID:', id);

  try {
    // استخدام دالة الحذف الشامل التي تحذف المنتج مع جميع تفاصيله
    return await deleteProductWithDetails(id);
  } catch (error) {
    console.error('❌ قاعدة البيانات - خطأ في حذف المنتج:', error);
    throw error;
  }
}

// ==================== دوال صور المنتجات ====================

// الحصول على صور منتج
export async function getProductImages(productId: string): Promise<ProductImage[]> {
  const query = `
    SELECT * FROM product_images
    WHERE product_id = ?
    ORDER BY sort_order ASC, created_at ASC
  `;
  return await executeQuery<ProductImage>(query, [productId]);
}

// إضافة صورة منتج
export async function addProductImage(productId: string, imageUrl: string, sortOrder: number = 0): Promise<ProductImage> {
  const query = `
    INSERT INTO product_images (product_id, image_url, sort_order)
    VALUES (?, ?, ?)
  `;

  const result = await executeInsert(query, [productId, imageUrl, sortOrder]);

  const newImage = await executeQuerySingle<ProductImage>(
    'SELECT * FROM product_images WHERE id = ?',
    [result.insertId]
  );

  if (!newImage) {
    throw new Error('Failed to create product image');
  }
  return newImage;
}

// حذف صورة منتج
export async function deleteProductImage(id: number): Promise<boolean> {
  const query = 'DELETE FROM product_images WHERE id = ?';
  const result = await executeUpdate(query, [id]);
  return result.affectedRows > 0;
}

// حذف جميع صور منتج
export async function deleteAllProductImages(productId: string): Promise<boolean> {
  const query = 'DELETE FROM product_images WHERE product_id = ?';
  const result = await executeUpdate(query, [productId]);
  return result.affectedRows > 0;
}

// ==================== دوال مميزات المنتجات ====================

// الحصول على مميزات منتج
export async function getProductFeatures(productId: string): Promise<ProductFeature[]> {
  const query = `
    SELECT * FROM product_features
    WHERE product_id = ?
    ORDER BY sort_order ASC
  `;
  return await executeQuery<ProductFeature>(query, [productId]);
}

// إضافة ميزة منتج
export async function addProductFeature(
  productId: string,
  featureText: string,
  featureTextAr: string,
  sortOrder: number = 0
): Promise<ProductFeature> {
  const query = `
    INSERT INTO product_features (product_id, feature_text, feature_text_ar, sort_order)
    VALUES (?, ?, ?, ?)
  `;

  const result = await executeInsert(query, [productId, featureText, featureTextAr, sortOrder]);

  const newFeature = await executeQuerySingle<ProductFeature>(
    'SELECT * FROM product_features WHERE id = ?',
    [result.insertId]
  );

  if (!newFeature) {
    throw new Error('Failed to create product feature');
  }
  return newFeature;
}

// حذف ميزة منتج
export async function deleteProductFeature(id: number): Promise<boolean> {
  const query = 'DELETE FROM product_features WHERE id = ?';
  const result = await executeUpdate(query, [id]);
  return result.affectedRows > 0;
}

// حذف جميع مميزات منتج
export async function deleteAllProductFeatures(productId: string): Promise<boolean> {
  const query = 'DELETE FROM product_features WHERE product_id = ?';
  const result = await executeUpdate(query, [productId]);
  return result.affectedRows > 0;
}

// ==================== دوال مواصفات المنتجات ====================

// الحصول على مواصفات منتج
export async function getProductSpecifications(productId: string): Promise<ProductSpecification[]> {
  const query = `
    SELECT * FROM product_specifications
    WHERE product_id = ?
    ORDER BY sort_order ASC
  `;
  return await executeQuery<ProductSpecification>(query, [productId]);
}

// إضافة مواصفة منتج
export async function addProductSpecification(
  productId: string,
  specKey: string,
  specKeyAr: string,
  specValue: string,
  specValueAr: string,
  sortOrder: number = 0
): Promise<ProductSpecification> {
  const query = `
    INSERT INTO product_specifications (product_id, spec_key, spec_key_ar, spec_value, spec_value_ar, sort_order)
    VALUES (?, ?, ?, ?, ?, ?)
  `;

  const result = await executeInsert(query, [productId, specKey, specKeyAr, specValue, specValueAr, sortOrder]);

  const newSpec = await executeQuerySingle<ProductSpecification>(
    'SELECT * FROM product_specifications WHERE id = ?',
    [result.insertId]
  );

  if (!newSpec) {
    throw new Error('Failed to create product specification');
  }
  return newSpec;
}

// حذف مواصفة منتج
export async function deleteProductSpecification(id: number): Promise<boolean> {
  const query = 'DELETE FROM product_specifications WHERE id = ?';
  const result = await executeUpdate(query, [id]);
  return result.affectedRows > 0;
}

// حذف جميع مواصفات منتج
export async function deleteAllProductSpecifications(productId: string): Promise<boolean> {
  const query = 'DELETE FROM product_specifications WHERE product_id = ?';
  const result = await executeUpdate(query, [productId]);
  return result.affectedRows > 0;
}

// ==================== دوال إدارة المنتجات المتكاملة ====================

// إضافة منتج مع جميع التفاصيل (صور، مميزات، مواصفات)
export async function addProductWithDetails(productData: {
  product: ProductInput;
  images?: string[];
  features?: { text: string; textAr: string }[];
  specifications?: { key: string; keyAr: string; value: string; valueAr: string }[];
}): Promise<ProductWithDetails> {
  const { product, images = [], features = [], specifications = [] } = productData;

  try {
    // إضافة المنتج الأساسي
    const newProduct = await addProduct(product);

    // إضافة الصور
    const productImages: ProductImage[] = [];
    for (let i = 0; i < images.length; i++) {
      if (images[i].trim()) {
        const image = await addProductImage(product.id, images[i], i);
        productImages.push(image);
      }
    }

    // إضافة المميزات
    const productFeatures: ProductFeature[] = [];
    for (let i = 0; i < features.length; i++) {
      if (features[i].text.trim() && features[i].textAr.trim()) {
        const feature = await addProductFeature(
          product.id,
          features[i].text,
          features[i].textAr,
          i
        );
        productFeatures.push(feature);
      }
    }

    // إضافة المواصفات
    const productSpecifications: ProductSpecification[] = [];
    for (let i = 0; i < specifications.length; i++) {
      if (specifications[i].key.trim() && specifications[i].value.trim()) {
        const spec = await addProductSpecification(
          product.id,
          specifications[i].key,
          specifications[i].keyAr,
          specifications[i].value,
          specifications[i].valueAr,
          i
        );
        productSpecifications.push(spec);
      }
    }

    return {
      ...newProduct,
      images: productImages,
      features: productFeatures,
      specifications: productSpecifications
    };
  } catch (error) {
    // في حالة حدوث خطأ، نحذف المنتج إذا تم إنشاؤه
    try {
      await deleteProduct(product.id);
    } catch (deleteError) {
      console.error('Error cleaning up product after failed creation:', deleteError);
    }
    throw error;
  }
}

// تحديث منتج مع جميع التفاصيل
export async function updateProductWithDetails(
  productId: string,
  updateData: {
    product?: ProductUpdate;
    images?: string[];
    features?: { text: string; textAr: string }[];
    specifications?: { key: string; keyAr: string; value: string; valueAr: string }[];
  }
): Promise<ProductWithDetails> {
  const { product, images, features, specifications } = updateData;

  try {
    // تحديث المنتج الأساسي إذا كانت هناك تحديثات
    let updatedProduct;
    if (product) {
      updatedProduct = await updateProduct(productId, product);
    } else {
      updatedProduct = await getProductById(productId);
      if (!updatedProduct) {
        throw new Error('Product not found');
      }
    }

    // تحديث الصور إذا تم تمريرها
    let productImages: ProductImage[] = [];
    if (images !== undefined) {
      // حذف الصور الحالية
      await deleteAllProductImages(productId);

      // إضافة الصور الجديدة
      for (let i = 0; i < images.length; i++) {
        if (images[i].trim()) {
          const image = await addProductImage(productId, images[i], i);
          productImages.push(image);
        }
      }
    } else {
      productImages = await getProductImages(productId);
    }

    // تحديث المميزات إذا تم تمريرها
    let productFeatures: ProductFeature[] = [];
    if (features !== undefined) {
      // حذف المميزات الحالية
      await deleteAllProductFeatures(productId);

      // إضافة المميزات الجديدة
      for (let i = 0; i < features.length; i++) {
        if (features[i].text.trim() && features[i].textAr.trim()) {
          const feature = await addProductFeature(
            productId,
            features[i].text,
            features[i].textAr,
            i
          );
          productFeatures.push(feature);
        }
      }
    } else {
      productFeatures = await getProductFeatures(productId);
    }

    // تحديث المواصفات إذا تم تمريرها
    let productSpecifications: ProductSpecification[] = [];
    if (specifications !== undefined) {
      // حذف المواصفات الحالية
      await deleteAllProductSpecifications(productId);

      // إضافة المواصفات الجديدة
      for (let i = 0; i < specifications.length; i++) {
        if (specifications[i].key.trim() && specifications[i].value.trim()) {
          const spec = await addProductSpecification(
            productId,
            specifications[i].key,
            specifications[i].keyAr,
            specifications[i].value,
            specifications[i].valueAr,
            i
          );
          productSpecifications.push(spec);
        }
      }
    } else {
      productSpecifications = await getProductSpecifications(productId);
    }

    return {
      ...updatedProduct,
      images: productImages,
      features: productFeatures,
      specifications: productSpecifications
    };
  } catch (error) {
    throw error;
  }
}

// حذف منتج مع جميع التفاصيل (hard delete)
export async function deleteProductWithDetails(productId: string): Promise<boolean> {
  console.log('📥 قاعدة البيانات - حذف منتج مع جميع التفاصيل - ID:', productId);

  try {
    // حذف جميع البيانات المرتبطة بالمنتج
    console.log('🗑️ قاعدة البيانات - حذف صور المنتج...');
    await deleteAllProductImages(productId);

    console.log('🗑️ قاعدة البيانات - حذف ميزات المنتج...');
    await deleteAllProductFeatures(productId);

    console.log('🗑️ قاعدة البيانات - حذف مواصفات المنتج...');
    await deleteAllProductSpecifications(productId);

    // حذف المنتج نفسه (hard delete)
    console.log('🗑️ قاعدة البيانات - حذف المنتج الرئيسي...');
    const deleteProductQuery = `
      DELETE FROM products
      WHERE id = ?
    `;
    const result = await executeUpdate(deleteProductQuery, [productId]);
    console.log('📦 قاعدة البيانات - نتيجة حذف المنتج:', result);

    const success = result.affectedRows > 0;
    console.log(success ? '✅ قاعدة البيانات - تم حذف المنتج نهائياً بنجاح' : '❌ قاعدة البيانات - لم يتم حذف أي منتج');
    return success;
  } catch (error) {
    console.error('❌ قاعدة البيانات - خطأ في حذف المنتج مع التفاصيل:', error);
    throw error;
  }
}



// جلب جميع المنتجات مع التفاصيل
export async function getProductsWithDetails(): Promise<ProductWithDetails[]> {
  try {
    const products = await getProducts();
    const productsWithDetails: ProductWithDetails[] = [];

    for (const product of products) {
      const [images, features, specifications] = await Promise.all([
        getProductImages(product.id),
        getProductFeatures(product.id),
        getProductSpecifications(product.id)
      ]);

      productsWithDetails.push({
        ...product,
        images,
        features,
        specifications
      });
    }

    return productsWithDetails;
  } catch (error) {
    throw error;
  }
}

// ==================== دوال طلبات التسعير ====================

// الحصول على جميع طلبات التسعير
export async function getQuoteRequests(): Promise<QuoteRequest[]> {
  const query = `
    SELECT * FROM quote_requests
    WHERE deleted_at IS NULL
    ORDER BY created_at DESC
  `;
  return await executeQuery<QuoteRequest>(query);
}

// الحصول على طلب تسعير بواسطة ID
export async function getQuoteRequestById(id: string): Promise<QuoteRequest | null> {
  const query = `
    SELECT * FROM quote_requests
    WHERE id = ? AND deleted_at IS NULL
  `;
  return await executeQuerySingle<QuoteRequest>(query, [id]);
}

// إضافة طلب تسعير جديد
export async function addQuoteRequest(quoteRequest: QuoteRequestInput): Promise<QuoteRequest> {
  const query = `
    INSERT INTO quote_requests (
      id, customer_name, customer_email, customer_phone, customer_company,
      excel_file_url, status, notes
    )
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `;

  await executeInsert(query, [
    quoteRequest.id,
    quoteRequest.customer_name,
    quoteRequest.customer_email,
    quoteRequest.customer_phone,
    quoteRequest.customer_company || null,
    quoteRequest.excel_file_url || null,
    quoteRequest.status,
    quoteRequest.notes || null
  ]);

  const newQuoteRequest = await getQuoteRequestById(quoteRequest.id);
  if (!newQuoteRequest) {
    throw new Error('Failed to create quote request');
  }
  return newQuoteRequest;
}

// تحديث طلب تسعير
export async function updateQuoteRequest(id: string, updates: QuoteRequestUpdate): Promise<QuoteRequest> {
  const setClause = [];
  const values = [];

  if (updates.customer_name !== undefined) {
    setClause.push('customer_name = ?');
    values.push(updates.customer_name);
  }
  if (updates.customer_email !== undefined) {
    setClause.push('customer_email = ?');
    values.push(updates.customer_email);
  }
  if (updates.customer_phone !== undefined) {
    setClause.push('customer_phone = ?');
    values.push(updates.customer_phone);
  }
  if (updates.customer_company !== undefined) {
    setClause.push('customer_company = ?');
    values.push(updates.customer_company);
  }
  if (updates.excel_file_url !== undefined) {
    setClause.push('excel_file_url = ?');
    values.push(updates.excel_file_url);
  }
  if (updates.status !== undefined) {
    setClause.push('status = ?');
    values.push(updates.status);
  }
  if (updates.notes !== undefined) {
    setClause.push('notes = ?');
    values.push(updates.notes);
  }

  setClause.push('updated_at = CURRENT_TIMESTAMP');
  values.push(id);

  const query = `
    UPDATE quote_requests
    SET ${setClause.join(', ')}
    WHERE id = ? AND deleted_at IS NULL
  `;

  const result = await executeUpdate(query, values);
  if (result.affectedRows === 0) {
    throw new Error('Quote request not found or no changes made');
  }

  const updatedQuoteRequest = await getQuoteRequestById(id);
  if (!updatedQuoteRequest) {
    throw new Error('Failed to retrieve updated quote request');
  }
  return updatedQuoteRequest;
}

// حذف طلب تسعير (soft delete)
export async function deleteQuoteRequest(id: string): Promise<boolean> {
  const query = `
    UPDATE quote_requests
    SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
    WHERE id = ? AND deleted_at IS NULL
  `;

  const result = await executeUpdate(query, [id]);
  return result.affectedRows > 0;
}

// إضافة منتج لطلب تسعير
export async function addQuoteRequestProduct(quoteId: string, productId: string): Promise<QuoteRequestProduct> {
  const query = `
    INSERT INTO quote_request_products (quote_id, product_id)
    VALUES (?, ?)
  `;

  const result = await executeInsert(query, [quoteId, productId]);

  const newQuoteProduct = await executeQuerySingle<QuoteRequestProduct>(
    'SELECT * FROM quote_request_products WHERE id = ?',
    [result.insertId]
  );

  if (!newQuoteProduct) {
    throw new Error('Failed to add product to quote request');
  }
  return newQuoteProduct;
}

// الحصول على منتجات طلب تسعير
export async function getQuoteRequestProducts(quoteId: string): Promise<QuoteRequestProduct[]> {
  const query = `
    SELECT * FROM quote_request_products
    WHERE quote_id = ?
    ORDER BY created_at ASC
  `;
  return await executeQuery<QuoteRequestProduct>(query, [quoteId]);
}

// إضافة سجل لطلب تسعير
export async function addQuoteRequestLog(
  quoteId: string,
  actionBy: string,
  actionType: 'note' | 'status_change',
  note: string
): Promise<QuoteRequestLog> {
  const query = `
    INSERT INTO quote_request_logs (quote_id, action_by, action_type, note)
    VALUES (?, ?, ?, ?)
  `;

  const result = await executeInsert(query, [quoteId, actionBy, actionType, note]);

  const newLog = await executeQuerySingle<QuoteRequestLog>(
    'SELECT * FROM quote_request_logs WHERE id = ?',
    [result.insertId]
  );

  if (!newLog) {
    throw new Error('Failed to add quote request log');
  }
  return newLog;
}

// الحصول على سجلات طلب تسعير
export async function getQuoteRequestLogs(quoteId: string): Promise<QuoteRequestLog[]> {
  const query = `
    SELECT * FROM quote_request_logs
    WHERE quote_id = ?
    ORDER BY created_at DESC
  `;
  return await executeQuery<QuoteRequestLog>(query, [quoteId]);
}



// الحصول على طلب تسعير مع تفاصيل المنتجات
export async function getQuoteRequestWithDetails(id: string): Promise<QuoteRequestWithDetails | null> {
  const quoteRequest = await getQuoteRequestById(id);
  if (!quoteRequest) {
    return null;
  }

  const products = await getQuoteRequestProducts(id);
  const logs = await getQuoteRequestLogs(id);

  return {
    ...quoteRequest,
    products,
    logs
  };
}

// الحصول على إحصائيات طلبات التسعير
export async function getQuoteRequestStats(): Promise<{
  total: number;
  pending: number;
  processed: number;
  rejected: number;
}> {
  const query = `
    SELECT
      COUNT(*) as total,
      SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
      SUM(CASE WHEN status = 'processed' THEN 1 ELSE 0 END) as processed,
      SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
    FROM quote_requests
    WHERE deleted_at IS NULL
  `;

  const result = await executeQuerySingle<{
    total: number;
    pending: number;
    processed: number;
    rejected: number;
  }>(query);

  return result || { total: 0, pending: 0, processed: 0, rejected: 0 };
}

// ==================== دوال معلومات التواصل ====================



// الحصول على معلومات التواصل
export async function getContactInfo(): Promise<ContactInfo | null> {
  const query = 'SELECT * FROM contact_info ORDER BY id DESC LIMIT 1';
  return await executeQuerySingle<ContactInfo>(query);
}

// تحديث معلومات التواصل
export async function updateContactInfo(
  email?: string,
  password?: string,
  host?: string,
  port?: number
): Promise<ContactInfo> {
  // التحقق من وجود سجل
  const existing = await getContactInfo();

  if (existing) {
    // تحديث السجل الموجود
    const setClause = [];
    const values = [];

    if (email !== undefined) {
      setClause.push('email = ?');
      values.push(email);
    }
    if (password !== undefined) {
      setClause.push('Password = ?');
      values.push(password);
    }
    if (host !== undefined) {
      setClause.push('host = ?');
      values.push(host);
    }
    if (port !== undefined) {
      setClause.push('port = ?');
      values.push(port);
    }


    setClause.push('updated_at = CURRENT_TIMESTAMP');
    values.push(existing.id);

    const query = `
      UPDATE contact_info
      SET ${setClause.join(', ')}
      WHERE id = ?
    `;

    await executeUpdate(query, values);
  } else {
    // إنشاء سجل جديد
    const query = `
      INSERT INTO contact_info (email, Password, host, port)
      VALUES (?, ?, ?, ?)
    `;

    await executeInsert(query, [
      email || null,
      password || null,
      host || 'smtp.hostinger.com',
      port || 465
    ]);
  }

  const updatedContactInfo = await getContactInfo();
  if (!updatedContactInfo) {
    throw new Error('Failed to update contact info');
  }
  return updatedContactInfo;
}

// ==================== دوال المديرين ====================

// الحصول على جميع المديرين النشطين
export async function getAdmins(): Promise<Admin[]> {
  const query = `
    SELECT * FROM admins
    WHERE deleted_at IS NULL AND is_active = 1
    ORDER BY created_at DESC
  `;
  return await executeQuery<Admin>(query);
}

// الحصول على مدير بواسطة ID
export async function getAdminById(id: number): Promise<Admin | null> {
  const query = `
    SELECT * FROM admins
    WHERE id = ? AND deleted_at IS NULL
  `;
  return await executeQuerySingle<Admin>(query, [id]);
}

// الحصول على مدير بواسطة اسم المستخدم
export async function getAdminByUsername(username: string): Promise<Admin | null> {
  const query = `
    SELECT * FROM admins
    WHERE username = ? AND deleted_at IS NULL
  `;
  return await executeQuerySingle<Admin>(query, [username]);
}

// الحصول على مدير بواسطة البريد الإلكتروني
export async function getAdminByEmail(email: string): Promise<Admin | null> {
  const query = `
    SELECT * FROM admins
    WHERE email = ? AND deleted_at IS NULL
  `;
  return await executeQuerySingle<Admin>(query, [email]);
}

// إضافة مدير جديد
export async function addAdmin(admin: AdminInput): Promise<Admin> {
  const query = `
    INSERT INTO admins (username, email, password_hash, is_active)
    VALUES (?, ?, ?, ?)
  `;

  const result = await executeInsert(query, [
    admin.username,
    admin.email,
    admin.password_hash,
    admin.is_active
  ]);

  const newAdmin = await getAdminById(result.insertId);
  if (!newAdmin) {
    throw new Error('Failed to create admin');
  }
  return newAdmin;
}

// تحديث مدير
export async function updateAdmin(id: number, updates: AdminUpdate): Promise<Admin> {
  const setClause = [];
  const values = [];

  if (updates.username !== undefined) {
    setClause.push('username = ?');
    values.push(updates.username);
  }
  if (updates.email !== undefined) {
    setClause.push('email = ?');
    values.push(updates.email);
  }
  if (updates.password_hash !== undefined) {
    setClause.push('password_hash = ?');
    values.push(updates.password_hash);
  }
  if (updates.is_active !== undefined) {
    setClause.push('is_active = ?');
    values.push(updates.is_active);
  }
  if (updates.last_login !== undefined) {
    setClause.push('last_login = ?');
    values.push(updates.last_login);
  }

  setClause.push('updated_at = CURRENT_TIMESTAMP');
  values.push(id);

  const query = `
    UPDATE admins
    SET ${setClause.join(', ')}
    WHERE id = ? AND deleted_at IS NULL
  `;

  const result = await executeUpdate(query, values);
  if (result.affectedRows === 0) {
    throw new Error('Admin not found or no changes made');
  }

  const updatedAdmin = await getAdminById(id);
  if (!updatedAdmin) {
    throw new Error('Failed to retrieve updated admin');
  }
  return updatedAdmin;
}

// حذف مدير (soft delete)
export async function deleteAdmin(id: number): Promise<boolean> {
  const query = `
    UPDATE admins
    SET deleted_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
    WHERE id = ? AND deleted_at IS NULL
  `;

  const result = await executeUpdate(query, [id]);
  return result.affectedRows > 0;
}

// تحديث آخر تسجيل دخول للمدير
export async function updateAdminLastLogin(id: number): Promise<boolean> {
  const query = `
    UPDATE admins
    SET last_login = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP
    WHERE id = ? AND deleted_at IS NULL
  `;

  const result = await executeUpdate(query, [id]);
  return result.affectedRows > 0;
}


