/**
 * مُحسن الأداء - مجموعة من الأدوات لتحسين أداء التطبيق
 */

// دالة لتأخير التحميل (Debounce)
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// دالة للتحكم في معدل الاستدعاء (Throttle)
export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// دالة لتحميل الصور بشكل كسول
export function lazyLoadImage(
  img: HTMLImageElement,
  src: string,
  placeholder?: string
): void {
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const image = entry.target as HTMLImageElement;
          image.src = src;
          image.onload = () => {
            image.classList.add('loaded');
          };
          observer.unobserve(image);
        }
      });
    },
    { threshold: 0.1 }
  );

  if (placeholder) {
    img.src = placeholder;
  }
  img.classList.add('lazy');
  observer.observe(img);
}

// دالة لقياس أداء الدوال
export function measurePerformance<T extends (...args: unknown[]) => unknown>(
  func: T,
  name: string
): T {
  return ((...args: Parameters<T>) => {
    const start = performance.now();
    const result = func(...args);
    const end = performance.now();
    console.log(`⏱️ ${name} took ${(end - start).toFixed(2)}ms`);
    return result;
  }) as T;
}

// دالة لقياس أداء الدوال غير المتزامنة
export function measureAsyncPerformance<T extends (...args: unknown[]) => Promise<unknown>>(
  func: T,
  name: string
): T {
  return (async (...args: Parameters<T>) => {
    const start = performance.now();
    const result = await func(...args);
    const end = performance.now();
    console.log(`⏱️ ${name} took ${(end - start).toFixed(2)}ms`);
    return result;
  }) as T;
}

// دالة لتحسين الصور
export function optimizeImageUrl(
  url: string,
  width?: number,
  height?: number,
  quality: number = 80
): string {
  if (!url) return '';
  
  // إذا كانت الصورة محلية، أضف معاملات التحسين
  if (url.startsWith('/') || url.includes(window.location.hostname)) {
    const params = new URLSearchParams();
    if (width) params.set('w', width.toString());
    if (height) params.set('h', height.toString());
    params.set('q', quality.toString());
    
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}${params.toString()}`;
  }
  
  return url;
}

// دالة لتحميل الموارد مسبقاً
export function preloadResource(href: string, as: string, type?: string): void {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = as;
  if (type) link.type = type;
  document.head.appendChild(link);
}

// دالة لتحميل الموارد بشكل مسبق
export function prefetchResource(href: string): void {
  const link = document.createElement('link');
  link.rel = 'prefetch';
  link.href = href;
  document.head.appendChild(link);
}

// دالة لتحسين الخطوط
export function preloadFont(href: string, type: string = 'font/woff2'): void {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = 'font';
  link.type = type;
  link.crossOrigin = 'anonymous';
  document.head.appendChild(link);
}

// دالة لمراقبة أداء الصفحة
export function monitorPagePerformance(): void {
  if (typeof window === 'undefined') return;

  window.addEventListener('load', () => {
    setTimeout(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const paint = performance.getEntriesByType('paint');
      
      console.group('📊 Page Performance Metrics');
      console.log(`🔄 DOM Content Loaded: ${navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart}ms`);
      console.log(`📄 Page Load: ${navigation.loadEventEnd - navigation.loadEventStart}ms`);
      console.log(`🌐 DNS Lookup: ${navigation.domainLookupEnd - navigation.domainLookupStart}ms`);
      console.log(`🔗 Connection: ${navigation.connectEnd - navigation.connectStart}ms`);
      console.log(`📡 Request: ${navigation.responseEnd - navigation.requestStart}ms`);
      
      paint.forEach((entry) => {
        console.log(`🎨 ${entry.name}: ${entry.startTime.toFixed(2)}ms`);
      });
      
      // Core Web Vitals
      if ('web-vitals' in window) {
        console.log('🎯 Core Web Vitals will be logged separately');
      }
      
      console.groupEnd();
    }, 0);
  });
}

// دالة لتحسين التمرير
export function optimizeScrolling(): void {
  if (typeof window === 'undefined') return;

  let ticking = false;

  function updateScrollPosition() {
    // منطق تحديث موقع التمرير
    ticking = false;
  }

  function requestTick() {
    if (!ticking) {
      requestAnimationFrame(updateScrollPosition);
      ticking = true;
    }
  }

  window.addEventListener('scroll', requestTick, { passive: true });
}

// دالة لتحسين الأحداث
export function optimizeEventListeners(): void {
  if (typeof window === 'undefined') return;

  // تحسين أحداث التمرير
  const scrollHandler = throttle(() => {
    // منطق معالجة التمرير
  }, 16); // 60fps

  // تحسين أحداث تغيير حجم النافذة
  const resizeHandler = debounce(() => {
    // منطق معالجة تغيير الحجم
  }, 250);

  window.addEventListener('scroll', scrollHandler, { passive: true });
  window.addEventListener('resize', resizeHandler);
}

// دالة لتنظيف الذاكرة
export function cleanupMemory(): void {
  // تنظيف المتغيرات العامة
  if (typeof window !== 'undefined') {
    // إزالة event listeners غير المستخدمة
    // تنظيف timers
    // مسح caches قديمة
  }
}

// دالة لتحسين الصور التفاعلية
export function setupResponsiveImages(): void {
  if (typeof window === 'undefined') return;

  const images = document.querySelectorAll('img[data-src]');
  
  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        const src = img.dataset.src;
        if (src) {
          img.src = src;
          img.removeAttribute('data-src');
          imageObserver.unobserve(img);
        }
      }
    });
  });

  images.forEach((img) => imageObserver.observe(img));
}

// دالة لتهيئة جميع التحسينات
export function initializePerformanceOptimizations(): void {
  if (typeof window === 'undefined') return;

  console.log('🚀 Initializing performance optimizations...');
  
  monitorPagePerformance();
  optimizeScrolling();
  optimizeEventListeners();
  setupResponsiveImages();
  
  console.log('✅ Performance optimizations initialized');
}
