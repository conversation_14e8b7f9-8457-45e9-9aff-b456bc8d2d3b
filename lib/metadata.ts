import { Metadata } from 'next';
import { Locale } from './i18n';

interface MetadataConfig {
  title: string;
  description: string;
  keywords?: string[];
  ogImage?: string;
  canonical?: string;
  noIndex?: boolean;
  alternateLanguages?: boolean;
}

export function generateSEOMetadata(
  locale: Locale,
  config: MetadataConfig
): Metadata {
  
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  
  // إنشاء العنوان الكامل
  const fullTitle = `${config.title} | ${siteName}`;
  
  // الكلمات المفتاحية الافتراضية
  const defaultKeywords = locale === 'ar' 
    ? ['دروب هاجر', 'متجر إلكتروني', 'تسوق أونلاين', 'منتجات عالية الجودة', 'السعودية', 'الرياض']
    : ['DROOB HAJER', 'online store', 'e-commerce', 'high quality products', 'Saudi Arabia', 'Riyadh'];
  
  const keywords = [...defaultKeywords, ...(config.keywords || [])];
  
  // إنشاء الـ canonical URL
  const canonicalUrl = config.canonical 
    ? `${baseUrl}/${locale}${config.canonical}`
    : `${baseUrl}/${locale}`;
  
  // إنشاء الـ Open Graph image
  const ogImageUrl = config.ogImage || `${baseUrl}/images/og-default.jpg`;
  
  const metadata: Metadata = {
    title: fullTitle,
    description: config.description,
    keywords: keywords.join(', '),
    
    // Open Graph
    openGraph: {
      title: fullTitle,
      description: config.description,
      url: canonicalUrl,
      siteName: siteName,
      images: [
        {
          url: ogImageUrl,
          width: 1200,
          height: 630,
          alt: config.title,
        }
      ],
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      type: 'website',
    },
    
    // Twitter Card
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description: config.description,
      images: [ogImageUrl],
      creator: '@droobhajer',
      site: '@droobhajer',
    },
    
    // Canonical URL
    alternates: {
      canonical: canonicalUrl,
      ...(config.alternateLanguages && {
        languages: {
          'ar-SA': `${baseUrl}/ar${config.canonical || ''}`,
          'en-US': `${baseUrl}/en${config.canonical || ''}`,
        }
      })
    },
    
    // Robots
    robots: {
      index: !config.noIndex,
      follow: !config.noIndex,
      googleBot: {
        index: !config.noIndex,
        follow: !config.noIndex,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    
    // Additional meta tags
    other: {
      'theme-color': '#3B82F6',
      'msapplication-TileColor': '#3B82F6',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
      'format-detection': 'telephone=no',
    },
  };
  
  return metadata;
}

// دوال مساعدة لصفحات محددة
export function generateHomeMetadata(locale: Locale): Metadata {
  
  return generateSEOMetadata(locale, {
    title: locale === 'ar' ? 'الصفحة الرئيسية - متجرك الموثوق للتسوق الإلكتروني' : 'Home - Your Trusted Online Shopping Store',
    description: locale === 'ar'
      ? 'منصة دروب هجر الرائدة في تجهيزات الفنادق ومستلزمات الضيافة في السعودية. نوفر سخانات بوفيه، عربات بوفيه، موزعات العصير، أدوات تقديم الطعام، ومعدات المطابخ الفندقية لفنادق مكة والمدينة والرياض وجدة. احصل على عروض أسعار مخصصة.'
      : 'DROOB HAJER leading platform for hotel equipment and hospitality supplies in Saudi Arabia. We provide chafing dishes, buffet trolleys, juice dispensers, food serving equipment, and hotel kitchen equipment for hotels in Makkah, Madinah, Riyadh, and Jeddah. Get customized quotes.',
    keywords: locale === 'ar'
      ? ['تجهيزات فندقية', 'مستلزمات الفنادق', 'تجهيزات فنادق السعودية', 'تجهيزات فنادق مكة', 'سخانات بوفيه', 'عربات بوفيه', 'موزعات العصير', 'مورد تجهيزات فنادق', 'عروض أسعار تجهيزات فندقية']
      : ['hotel equipment', 'hotel supplies', 'hotel equipment Saudi Arabia', 'hotel supplies Makkah', 'chafing dishes', 'buffet trolleys', 'juice dispensers', 'hotel supply supplier', 'hotel equipment quotation'],
    canonical: '',
    alternateLanguages: true,
  });
}

export function generateProductsMetadata(locale: Locale): Metadata {
  return generateSEOMetadata(locale, {
    title: locale === 'ar' ? 'جميع المنتجات - تشكيلة واسعة ومتنوعة' : 'All Products - Wide and Diverse Collection',
    description: locale === 'ar'
      ? 'تصفح جميع منتجاتنا المتنوعة وعالية الجودة. اكتشف أحدث العروض والمنتجات الجديدة مع أفضل الأسعار وضمان الجودة.'
      : 'Browse all our diverse and high-quality products. Discover the latest offers and new products with the best prices and quality guarantee.',
    keywords: locale === 'ar'
      ? ['منتجات', 'تسوق', 'عروض', 'أسعار مميزة', 'جودة عالية']
      : ['products', 'shopping', 'offers', 'great prices', 'high quality'],
    canonical: '/products',
    alternateLanguages: true,
  });
}

export function generateCategoriesMetadata(locale: Locale): Metadata {
  return generateSEOMetadata(locale, {
    title: locale === 'ar' ? 'فئات المنتجات - تصفح حسب الفئة' : 'Product Categories - Browse by Category',
    description: locale === 'ar'
      ? 'استكشف فئات منتجاتنا المتنوعة. تصفح بسهولة حسب الفئة للعثور على ما تبحث عنه بسرعة وسهولة.'
      : 'Explore our diverse product categories. Browse easily by category to find what you are looking for quickly and easily.',
    keywords: locale === 'ar'
      ? ['فئات', 'تصنيفات', 'منتجات متنوعة', 'تصفح سهل']
      : ['categories', 'classifications', 'diverse products', 'easy browsing'],
    canonical: '/categories',
    alternateLanguages: true,
  });
}

export function generateAboutMetadata(locale: Locale): Metadata {
  return generateSEOMetadata(locale, {
    title: locale === 'ar' ? 'من نحن - قصة دروب هاجر ورؤيتنا' : 'About Us - DROOB HAJER Story and Vision',
    description: locale === 'ar'
      ? 'تعرف على قصة دروب هاجر ورؤيتنا في تقديم أفضل تجربة تسوق إلكتروني. نحن ملتزمون بالجودة والثقة وخدمة العملاء المتميزة.'
      : 'Learn about DROOB HAJER story and our vision in providing the best online shopping experience. We are committed to quality, trust and excellent customer service.',
    keywords: locale === 'ar'
      ? ['من نحن', 'قصتنا', 'رؤيتنا', 'قيمنا', 'التزامنا']
      : ['about us', 'our story', 'our vision', 'our values', 'our commitment'],
    canonical: '/about',
    alternateLanguages: true,
  });
}

export function generateContactMetadata(locale: Locale): Metadata {
  return generateSEOMetadata(locale, {
    title: locale === 'ar' ? 'تواصل معنا - خدمة العملاء والدعم' : 'Contact Us - Customer Service and Support',
    description: locale === 'ar'
      ? 'تواصل مع فريق خدمة العملاء في دروب هاجر. نحن هنا لمساعدتك في أي استفسار أو طلب. خدمة عملاء متاحة 24/7.'
      : 'Contact DROOB HAJER customer service team. We are here to help you with any inquiry or request. Customer service available 24/7.',
    keywords: locale === 'ar'
      ? ['تواصل معنا', 'خدمة العملاء', 'دعم', 'مساعدة', 'استفسارات']
      : ['contact us', 'customer service', 'support', 'help', 'inquiries'],
    canonical: '/contact',
    alternateLanguages: true,
  });
}

export function generateCartMetadata(locale: Locale): Metadata {
  return generateSEOMetadata(locale, {
    title: locale === 'ar' ? 'سلة التسوق - مراجعة طلبك' : 'Shopping Cart - Review Your Order',
    description: locale === 'ar'
      ? 'راجع منتجاتك في سلة التسوق وأكمل عملية الشراء بأمان. دفع آمن وشحن سريع مضمون.'
      : 'Review your products in the shopping cart and complete the purchase safely. Secure payment and fast shipping guaranteed.',
    keywords: locale === 'ar'
      ? ['سلة التسوق', 'مراجعة الطلب', 'دفع آمن', 'شحن سريع']
      : ['shopping cart', 'order review', 'secure payment', 'fast shipping'],
    canonical: '/cart',
    noIndex: true, // سلة التسوق لا تحتاج فهرسة
  });
}
