export interface AdminUser {
  id: string;
  username: string;
  password: string;
  email: string;
  role: 'admin' | 'moderator';
  lastLogin?: Date;
}

export interface SiteSettings {
  siteName: string;
  siteNameAr: string;
  contactEmail: string;
  whatsappNumber: string;
  socialLinks: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
    youtube?: string;
  };
  heroImages: string[];
  aboutText: string;
  aboutTextAr: string;
  address: string;
  addressAr: string;
  phone: string;
  workingHours: string;
  workingHoursAr: string;
  // Contact page settings
  contactSettings: {
    mapSettings: {
      latitude: number;
      longitude: number;
      zoom: number;
      googleMapsUrl?: string;
      showMap: boolean;
    };
    contactFormSettings: {
      enableContactForm: boolean;
      successMessage: string;
      successMessageAr: string;
      errorMessage: string;
      errorMessageAr: string;
    };
    officeHours: {
      enabled: boolean;
      hoursText: string;
      hoursTextAr: string;
    };
    additionalInfo: {
      description: string;
      descriptionAr: string;
      showFAQ: boolean;
    };
  };
  // About page settings
  aboutSettings: {
    heroSection: {
      title: string;
      titleAr: string;
      subtitle: string;
      subtitleAr: string;
    };
    description: {
      text: string;
      textAr: string;
    };
    vision: {
      title: string;
      titleAr: string;
      text: string;
      textAr: string;
    };
    mission: {
      title: string;
      titleAr: string;
      text: string;
      textAr: string;
    };
    values: {
      title: string;
      titleAr: string;
      items: Array<{
        titleEn: string;
        titleAr: string;
        descriptionEn: string;
        descriptionAr: string;
        icon: string;
      }>;
    };
    team: {
      title: string;
      titleAr: string;
      description: string;
      descriptionAr: string;
    };
    contactCTA: {
      title: string;
      titleAr: string;
      description: string;
      descriptionAr: string;
      enabled: boolean;
    };
  };
  // Partners settings (for homepage)
  partnersSettings: {
    title: string;
    titleAr: string;
    description: string;
    descriptionAr: string;
    enabled: boolean;
    items: Array<{
      nameEn: string;
      nameAr: string;
      logo: string;
      website?: string;
      description?: string;
      descriptionAr?: string;
    }>;
  };
  // Header settings
  headerSettings: {
    logo?: string;
    showLanguageSwitch: boolean;
    showSearchBar: boolean;
    navigationItems: Array<{
      nameEn: string;
      nameAr: string;
      url: string;
      isActive: boolean;
    }>;
  };
  // Footer settings
  footerSettings: {
    copyrightText: string;
    copyrightTextAr: string;
    showSocialLinks: boolean;
    quickLinks: Array<{
      nameEn: string;
      nameAr: string;
      url: string;
      isActive: boolean;
    }>;
    companyInfo: {
      description: string;
      descriptionAr: string;
      showAddress: boolean;
      showPhone: boolean;
      showEmail: boolean;
      showWorkingHours: boolean;
    };
  };
  // Communication settings for quote requests
  communicationSettings?: {
    // Email settings
    email: {
      smtpHost: string;
      smtpPort: number;
      smtpSecure: boolean;
      smtpUser: string;
      smtpPass: string;
      adminEmail: string;
      fromName: string;
      fromNameAr: string;
      enabled: boolean;
    };
    // WhatsApp settings
    whatsapp: {
      businessNumber: string;
      welcomeMessage: string;
      welcomeMessageAr: string;
      quoteResponseMessage: string;
      quoteResponseMessageAr: string;
      enabled: boolean;
    };
  };
}

export interface Category {
  id: string;
  name: string;
  nameAr: string;
  description?: string;
  descriptionAr?: string;
  image?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Subcategory {
  id: string;
  name: string;
  nameAr: string;
  categoryId: string;
  description?: string;
  descriptionAr?: string;
  image?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface SpecificationItem {
  nameEn: string;
  nameAr: string;
  valueEn: string;
  valueAr: string;
}

export interface AdminProduct {
  id: string;
  title: string;
  titleAr: string;
  description: string;
  descriptionAr: string;
  images: string[];
  price: number;
  originalPrice?: number;
  available: boolean;
  categoryId: string;
  subcategoryId: string;
  features: string[];
  featuresAr: string[];
  specifications: SpecificationItem[];
  isActive: boolean;
  isFeatured: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface DashboardStats {
  totalProducts: number;
  totalCategories: number;
  totalSubcategories: number;
  activeProducts: number;
  inactiveProducts: number;
  featuredProducts: number;
}

export interface QuoteRequest {
  id: string;
  customerInfo: {
    name: string;
    email: string;
    phone: string;
    company: string;
  };
  products: CartItem[];
  totalAmount: number;
  createdAt: string;
  status: 'pending' | 'processed' | 'sent';
  excelFilePath: string;
}

export interface CartItem {
  id: string;
  title: string;
  titleAr: string;
  price: number;
  quantity: number;
  image: string;
}

export interface CompanySettings {
  email: string;
  whatsapp: string;
}
