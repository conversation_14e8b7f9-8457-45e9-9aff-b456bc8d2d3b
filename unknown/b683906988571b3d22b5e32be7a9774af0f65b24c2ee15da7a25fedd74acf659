import { Metadata } from 'next';
import { Locale } from '@/lib/i18n';
import { getCitySpecificKeywords } from '@/lib/main-keywords';

interface Category {
  id: number;
  name: string;
  name_ar: string;
  description?: string;
  description_ar?: string;
  products_count?: number;
}

interface CategorySEOProps {
  category: Category;
  locale: Locale;
  city?: string;
}

export function generateCategoryMetadata(
  category: Category,
  locale: Locale,
  city?: string
): Metadata {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  
  const categoryName = locale === 'ar' ? category.name_ar : category.name;
  const categoryDescription = locale === 'ar' ? category.description_ar : category.description;
  
  // إنشاء العنوان مع المدينة إذا كانت محددة
  const title = locale === 'ar'
    ? `${categoryName} ${city ? `- ${getCityNameInArabic(city)}` : ''} | تجهيزات فندقية | ${siteName}`
    : `${categoryName} ${city ? `- ${getCityNameInEnglish(city)}` : ''} | Hotel Equipment | ${siteName}`;

  // إنشاء الوصف
  const cityText = city ? (locale === 'ar' 
    ? `في ${getCityNameInArabic(city)}` 
    : `in ${getCityNameInEnglish(city)}`) : '';
    
  const description = locale === 'ar'
    ? `استكشف مجموعة ${categoryName} الشاملة ${cityText}. ${categoryDescription || 'تجهيزات فندقية عالية الجودة'}. ${category.products_count ? `أكثر من ${category.products_count} منتج متاح` : ''}. احصل على عروض أسعار مخصصة من مورد تجهيزات فنادق موثوق.`
    : `Explore our comprehensive ${categoryName} collection ${cityText}. ${categoryDescription || 'High quality hotel equipment'}. ${category.products_count ? `Over ${category.products_count} products available` : ''}. Get custom quotes from a trusted hotel supply supplier.`;

  // إنشاء الكلمات المفتاحية
  const baseKeywords = locale === 'ar'
    ? ['تجهيزات فندقية', 'مستلزمات الفنادق', 'معدات الفنادق', 'مورد تجهيزات فنادق']
    : ['hotel equipment', 'hotel supplies', 'hospitality supplies', 'hotel supply supplier'];

  const categoryKeywords = locale === 'ar'
    ? [
        categoryName,
        `${categoryName} فندقية`,
        `${categoryName} للفنادق`,
        `فئة ${categoryName}`,
        `أقسام ${categoryName}`,
        'فئات تجهيزات فندقية'
      ]
    : [
        categoryName,
        `hotel ${categoryName.toLowerCase()}`,
        `${categoryName.toLowerCase()} for hotels`,
        `${categoryName.toLowerCase()} category`,
        `${categoryName.toLowerCase()} section`,
        'hotel equipment categories'
      ];

  const cityKeywords = city ? getCitySpecificKeywords(city, locale) : [];
  
  const keywords = [...baseKeywords, ...categoryKeywords, ...cityKeywords].join(', ');

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      url: `${baseUrl}/${locale}/category/${category.id}`,
      siteName,
      type: 'website',
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    },
    alternates: {
      canonical: `${baseUrl}/${locale}/category/${category.id}`,
      languages: {
        'ar': `${baseUrl}/ar/category/${category.id}`,
        'en': `${baseUrl}/en/category/${category.id}`,
      },
    },
  };
}

// مكون لـ JSON-LD للفئة
export function CategoryJsonLd({ category, locale }: CategorySEOProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';
  
  const categoryName = locale === 'ar' ? category.name_ar : category.name;
  const categoryDescription = locale === 'ar' ? category.description_ar : category.description;
  
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: categoryName,
    description: categoryDescription || (locale === 'ar' 
      ? `فئة ${categoryName} - تجهيزات فندقية عالية الجودة من دروب هجر`
      : `${categoryName} Category - High quality hotel equipment from DROOB HAJER`
    ),
    url: `${baseUrl}/${locale}/category/${category.id}`,
    isPartOf: {
      '@type': 'WebSite',
      name: siteName,
      url: baseUrl,
    },
    breadcrumb: {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: locale === 'ar' ? 'الرئيسية' : 'Home',
          item: `${baseUrl}/${locale}`,
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: locale === 'ar' ? 'الفئات' : 'Categories',
          item: `${baseUrl}/${locale}/categories`,
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: categoryName,
          item: `${baseUrl}/${locale}/category/${category.id}`,
        },
      ],
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
    />
  );
}

// دوال مساعدة للمدن
function getCityNameInArabic(city: string): string {
  const cityNames: Record<string, string> = {
    makkah: 'مكة المكرمة',
    madinah: 'المدينة المنورة',
    riyadh: 'الرياض',
    jeddah: 'جدة',
  };
  return cityNames[city] || city;
}

function getCityNameInEnglish(city: string): string {
  const cityNames: Record<string, string> = {
    makkah: 'Makkah',
    madinah: 'Madinah',
    riyadh: 'Riyadh',
    jeddah: 'Jeddah',
  };
  return cityNames[city] || city;
}

// دالة لإنشاء كلمات مفتاحية خاصة بالفئة والمدينة
export function generateCategoryKeywords(
  category: Category,
  locale: Locale,
  city?: string
): string[] {
  const categoryName = locale === 'ar' ? category.name_ar : category.name;
  
  const baseKeywords = locale === 'ar'
    ? ['تجهيزات فندقية', 'مستلزمات الفنادق', 'معدات الفنادق']
    : ['hotel equipment', 'hotel supplies', 'hospitality supplies'];

  const categoryKeywords = locale === 'ar'
    ? [
        categoryName,
        `${categoryName} فندقية`,
        `${categoryName} للفنادق`,
        `فئة ${categoryName}`,
        'فئات تجهيزات فندقية'
      ]
    : [
        categoryName,
        `hotel ${categoryName.toLowerCase()}`,
        `${categoryName.toLowerCase()} for hotels`,
        `${categoryName.toLowerCase()} category`,
        'hotel equipment categories'
      ];

  const cityKeywords = city ? (locale === 'ar'
    ? [
        `${categoryName} ${getCityNameInArabic(city)}`,
        `تجهيزات فنادق ${getCityNameInArabic(city)}`,
        `${categoryName} فنادق ${getCityNameInArabic(city)}`
      ]
    : [
        `${categoryName} ${getCityNameInEnglish(city)}`,
        `hotel supplies ${getCityNameInEnglish(city)}`,
        `${categoryName} hotels ${getCityNameInEnglish(city)}`
      ]) : [];

  return [...baseKeywords, ...categoryKeywords, ...cityKeywords];
}
