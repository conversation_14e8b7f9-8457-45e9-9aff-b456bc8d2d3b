#!/bin/bash

# 🚀 إعداد سريع للنشر على Hostinger
# مشروع دروب هاجر - droobhajer.com

echo "🚀 إعداد مشروع دروب هاجر للنشر على Hostinger..."

# ===== إنشاء ملف .env للإنتاج =====
echo "📝 إنشاء ملف .env للإنتاج..."

cat > .env.local << 'EOF'
# إعدادات الإنتاج - Hostinger Server
# مشروع دروب هاجر - droobhajer.com

# تكوين البيئة
NEXT_PUBLIC_APP_URL=http://droobhajer.com
NEXT_PUBLIC_APP_NAME=droobhajer
NEXT_TELEMETRY_DISABLED=1
NODE_ENV=production

# إعدادات الأمان - مفاتيح آمنة للإنتاج
JWT_SECRET=droobhajer-production-jwt-secret-2024-very-secure-91108112100-DROOBHAJER
ENCRYPTION_KEY=droobhajer-production-encryption-2024-very-secure-91108112100-DROOBHAJER
DEFAULT_ADMIN_PASSWORD=DroobHajer@2024!ProductionAdmin#Secure

# إعدادات الإيميل - سيتم جلبها من قاعدة البيانات
# لا حاجة لإعدادات الإيميل هنا - مخزنة في قاعدة البيانات

# إعدادات قاعدة البيانات MySQL - Hostinger
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=
DB_NAME=droobhajer_db

# إعدادات إضافية للإنتاج
NEXT_PUBLIC_SITE_URL=http://droobhajer.com
NEXT_PUBLIC_API_URL=http://droobhajer.com/api

# إعدادات الأمان الإضافية
NEXTAUTH_URL=http://droobhajer.com
NEXTAUTH_SECRET=droobhajer-nextauth-secret-2024-production-DROOBHAJER

# إعدادات التخزين
UPLOAD_DIR=/var/www/html/uploads
SECURE_UPLOAD_DIR=/var/www/html/secure-uploads

# إعدادات الجلسات
SESSION_SECRET=droobhajer-session-secret-2024-production-DROOBHAJER
COOKIE_DOMAIN=droobhajer.com
EOF

echo "✅ تم إنشاء ملف .env.local"

# ===== بناء المشروع =====
echo "🔨 بناء المشروع..."

# تنظيف البناء السابق
rm -rf .next
rm -rf node_modules

# تثبيت Dependencies
npm install

# بناء المشروع
npm run build

if [ $? -eq 0 ]; then
    echo "✅ تم بناء المشروع بنجاح"
else
    echo "❌ فشل في بناء المشروع!"
    exit 1
fi

# ===== إنشاء أرشيف للنشر =====
echo "📦 إنشاء أرشيف للنشر..."

# إنشاء مجلد مؤقت
mkdir -p deploy_package

# نسخ الملفات المطلوبة
cp -r .next deploy_package/
cp -r public deploy_package/
cp -r styles deploy_package/
cp package.json deploy_package/
cp package-lock.json deploy_package/
cp next.config.js deploy_package/
cp .env.local deploy_package/
cp ecosystem.config.js deploy_package/
cp .htaccess deploy_package/
cp database-setup-production.sql deploy_package/

# إنشاء مجلد logs
mkdir -p deploy_package/logs

# ضغط الملفات
tar -czf droobhajer-production.tar.gz -C deploy_package .

# تنظيف المجلد المؤقت
rm -rf deploy_package

echo "✅ تم إنشاء أرشيف النشر: droobhajer-production.tar.gz"

# ===== تعليمات النشر =====
echo ""
echo "🎉 المشروع جاهز للنشر!"
echo ""
echo "📋 خطوات النشر على Hostinger:"
echo "1. ارفع ملف droobhajer-production.tar.gz إلى /var/www/html/"
echo "2. استخرج الملفات: tar -xzf droobhajer-production.tar.gz"
echo "3. قم بتشغيل: npm install --production"
echo "4. أنشئ قاعدة البيانات وشغل: database-setup-production.sql"
echo "5. قم بتشغيل: pm2 start ecosystem.config.js --env production"
echo ""
echo "🌐 الموقع سيكون متاح على: http://droobhajer.com"
echo "🔧 لوحة الإدارة: http://droobhajer.com/admin"
echo "👤 اسم المستخدم: admin"
echo "🔑 كلمة المرور: DroobHajer@2024!ProductionAdmin#Secure"
echo ""
echo "⚠️  لا تنس تغيير كلمة مرور المدير بعد أول تسجيل دخول!"

echo ""
echo "📞 للمساعدة، راجع الملفات:"
echo "- HOSTINGER_DEPLOYMENT_GUIDE.md"
echo "- QUICK_DEPLOYMENT_STEPS.md"
